# خدماتي - <PERSON><PERSON><PERSON><PERSON>

تطبيق موبايل لربط العملاء بمقدمي الخدمات العامة في موريتانيا

## وصف التطبيق

"خدماتي" هو تطبيق Flutter متعدد الخدمات يهدف إلى ربط العملاء الذين يحتاجون خدمات منزلية ومهنية مع مقدمي الخدمات المؤهلين في موريتانيا. يوفر التطبيق منصة سهلة الاستخدام لطلب الخدمات وإدارتها وتقييمها.

## المميزات الرئيسية

### للعملاء
- تصفح فئات الخدمات المختلفة
- البحث عن مقدمي الخدمات القريبين
- إنشاء طلبات خدمة مفصلة
- تتبع حالة الطلبات
- تقييم مقدمي الخدمات
- التواصل المباشر مع مقدمي الخدمات

### لمقدمي الخدمات
- إنشاء ملف تعريفي احترافي
- عرض معرض الأعمال السابقة
- استقبال طلبات الخدمة
- إدارة الطلبات وحالاتها
- تحديد الأسعار والمواعيد
- إدارة التوفر والمواعيد

### للمشرفين
- إدارة المستخدمين ومقدمي الخدمات
- مراجعة والموافقة على الحسابات الجديدة
- مراقبة جودة الخدمات والتقييمات
- إدارة فئات الخدمات
- إحصائيات ومراقبة النشاط

## فئات الخدمات

- 🔧 السباكة
- 🪚 النجارة  
- ⚡ الكهرباء
- 🎨 الدهان
- 🔨 إصلاح الأجهزة
- 🚚 نقل الأثاث
- 🌱 خدمات الحدائق

## التقنيات المستخدمة

- **Flutter** - إطار العمل الأساسي
- **Firebase Authentication** - نظام المصادقة
- **Cloud Firestore** - قاعدة البيانات
- **Firebase Storage** - تخزين الصور
- **Provider** - إدارة الحالة
- **Go Router** - التنقل بين الشاشات
- **Get It** - حقن التبعيات

## المكتبات المستخدمة

```yaml
dependencies:
  flutter:
    sdk: flutter
  
  # UI & Navigation
  go_router: ^14.6.2
  provider: ^6.1.2
  get_it: ^8.0.2
  
  # Firebase
  firebase_core: ^4.0.0
  cloud_firestore: ^6.0.0
  firebase_storage: ^13.0.0
  firebase_messaging: ^16.0.0
  firebase_auth: ^6.0.1
  
  # Internationalization
  flutter_localizations:
    sdk: flutter
  intl: ^0.20.2
  
  # Location Services
  geolocator: ^13.0.2
  geocoding: ^3.0.0
  
  # Image Handling
  image_picker: ^1.1.2
  cached_network_image: ^3.4.1
  
  # Utilities
  shared_preferences: ^2.3.3
  uuid: ^4.5.1
  url_launcher: ^6.3.1
  
  # UI Components
  flutter_rating_bar: ^4.0.1
  shimmer: ^3.0.0
```

## بنية المشروع

```
lib/
├── core/
│   ├── constants/
│   │   └── app_constants.dart
│   ├── models/
│   │   ├── user_model.dart
│   │   ├── service_request_model.dart
│   │   └── review_model.dart
│   ├── routes/
│   │   └── app_router.dart
│   ├── services/
│   │   ├── service_locator.dart
│   │   ├── firebase_service.dart
│   │   ├── storage_service.dart
│   │   └── location_service.dart
│   └── theme/
│       ├── app_colors.dart
│       └── app_theme.dart
├── features/
│   ├── auth/
│   │   ├── providers/
│   │   │   └── auth_provider.dart
│   │   └── screens/
│   │       ├── user_type_selection_screen.dart
│   │       ├── login_screen.dart
│   │       └── register_screen.dart
│   ├── home/
│   │   └── screens/
│   │       └── home_screen.dart
│   ├── services/
│   │   ├── providers/
│   │   │   └── services_provider.dart
│   │   └── screens/
│   │       ├── service_categories_screen.dart
│   │       ├── service_providers_screen.dart
│   │       ├── service_provider_profile_screen.dart
│   │       ├── create_request_screen.dart
│   │       ├── request_details_screen.dart
│   │       └── my_requests_screen.dart
│   ├── location/
│   │   └── providers/
│   │       └── location_provider.dart
│   ├── profile/
│   │   └── screens/
│   │       ├── profile_screen.dart
│   │       └── edit_profile_screen.dart
│   └── admin/
│       └── screens/
│           └── admin_dashboard_screen.dart
├── firebase_options.dart
└── main.dart
```

## إعداد المشروع

### المتطلبات الأساسية
- Flutter SDK (3.8.1 أو أحدث)
- Dart SDK
- Android Studio / VS Code
- حساب Firebase

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd khadamaty
```

2. **تثبيت المكتبات**
```bash
flutter pub get
```

3. **إعداد Firebase**
   - إنشاء مشروع Firebase جديد
   - تفعيل Authentication, Firestore, Storage
   - تحديث ملف `firebase_options.dart` بالمعلومات الصحيحة

4. **تشغيل التطبيق**
```bash
flutter run
```

## إعداد Firebase

### 1. Authentication
- تفعيل Email/Password authentication
- إضافة المجالات المصرح بها

### 2. Firestore Database
إنشاء المجموعات التالية:
- `users` - بيانات المستخدمين
- `requests` - طلبات الخدمات
- `reviews` - التقييمات والآراء
- `notifications` - الإشعارات

### 3. Storage
إنشاء المجلدات التالية:
- `profile_images/` - صور الملفات الشخصية
- `service_images/` - صور طلبات الخدمة
- `portfolio_images/` - صور معرض الأعمال

### 4. Security Rules

#### Firestore Rules
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users collection
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow read: if request.auth != null;
    }
    
    // Requests collection
    match /requests/{requestId} {
      allow read, write: if request.auth != null;
    }
    
    // Reviews collection
    match /reviews/{reviewId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == resource.data.customerId;
    }
  }
}
```

#### Storage Rules
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /{allPaths=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

## دعم اللغات

التطبيق يدعم ثلاث لغات:
- 🇸🇦 العربية (اللغة الأساسية)
- 🇫🇷 الفرنسية
- 🇺🇸 الإنجليزية

## الحالة الحالية للمشروع

### ✅ مكتمل
- البنية الأساسية للمشروع
- نظام المصادقة (تسجيل الدخول والاشتراك)
- الشاشة الرئيسية
- تصفح فئات الخدمات
- عرض مقدمي الخدمات
- ملفات مقدمي الخدمات
- إنشاء طلبات الخدمة
- خدمات الموقع الجغرافي
- إدارة الحالة والتنقل

### 🚧 قيد التطوير
- تفاصيل الطلبات وإدارتها
- نظام التقييمات والمراجعات
- الملفات الشخصية وتعديلها
- لوحة الإدارة
- نظام الإشعارات
- الدردشة بين المستخدمين

### 📋 مخطط للمستقبل
- دعم المدفوعات الإلكترونية
- نظام الحجوزات المتقدم
- تطبيق الويب
- تحليلات متقدمة
- دعم لغات إضافية

## المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء branch جديد (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add some amazing feature'`)
4. Push إلى Branch (`git push origin feature/amazing-feature`)
5. فتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## التواصل

- البريد الإلكتروني: <EMAIL>
- الموقع الإلكتروني: https://khadamaty.mr
- تويتر: @KhadamatyApp

## شكر وتقدير

- فريق Flutter لإطار العمل الرائع
- Firebase لخدمات البنية التحتية
- مجتمع المطورين العرب للدعم والمساعدة

---

**خدماتي - ربط العملاء بمقدمي الخدمات العامة في موريتانيا** 🇲🇷