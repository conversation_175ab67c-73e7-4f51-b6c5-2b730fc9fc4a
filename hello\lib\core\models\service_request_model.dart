import 'package:cloud_firestore/cloud_firestore.dart';

class ServiceRequestModel {
  final String id;
  final String customerId;
  final String? serviceProviderId;
  final String serviceCategory;
  final String title;
  final String description;
  final String status; // pending, accepted, in_progress, completed, cancelled
  final double? proposedPrice;
  final double? agreedPrice;
  final String? customerAddress;
  final double? customerLatitude;
  final double? customerLongitude;
  final DateTime requestedDate;
  final DateTime? scheduledDate;
  final DateTime? completedDate;
  final List<String>? imageUrls;
  final String? customerNotes;
  final String? providerNotes;
  final DateTime createdAt;
  final DateTime updatedAt;

  ServiceRequestModel({
    required this.id,
    required this.customerId,
    this.serviceProviderId,
    required this.serviceCategory,
    required this.title,
    required this.description,
    required this.status,
    this.proposedPrice,
    this.agreedPrice,
    this.customerAddress,
    this.customerLatitude,
    this.customerLongitude,
    required this.requestedDate,
    this.scheduledDate,
    this.completedDate,
    this.imageUrls,
    this.customerNotes,
    this.providerNotes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ServiceRequestModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return ServiceRequestModel(
      id: doc.id,
      customerId: data['customerId'] ?? '',
      serviceProviderId: data['serviceProviderId'],
      serviceCategory: data['serviceCategory'] ?? '',
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      status: data['status'] ?? 'pending',
      proposedPrice: data['proposedPrice']?.toDouble(),
      agreedPrice: data['agreedPrice']?.toDouble(),
      customerAddress: data['customerAddress'],
      customerLatitude: data['customerLatitude']?.toDouble(),
      customerLongitude: data['customerLongitude']?.toDouble(),
      requestedDate: (data['requestedDate'] as Timestamp).toDate(),
      scheduledDate: data['scheduledDate'] != null
          ? (data['scheduledDate'] as Timestamp).toDate()
          : null,
      completedDate: data['completedDate'] != null
          ? (data['completedDate'] as Timestamp).toDate()
          : null,
      imageUrls: data['imageUrls'] != null
          ? List<String>.from(data['imageUrls'])
          : null,
      customerNotes: data['customerNotes'],
      providerNotes: data['providerNotes'],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'customerId': customerId,
      'serviceProviderId': serviceProviderId,
      'serviceCategory': serviceCategory,
      'title': title,
      'description': description,
      'status': status,
      'proposedPrice': proposedPrice,
      'agreedPrice': agreedPrice,
      'customerAddress': customerAddress,
      'customerLatitude': customerLatitude,
      'customerLongitude': customerLongitude,
      'requestedDate': Timestamp.fromDate(requestedDate),
      'scheduledDate': scheduledDate != null
          ? Timestamp.fromDate(scheduledDate!)
          : null,
      'completedDate': completedDate != null
          ? Timestamp.fromDate(completedDate!)
          : null,
      'imageUrls': imageUrls,
      'customerNotes': customerNotes,
      'providerNotes': providerNotes,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  ServiceRequestModel copyWith({
    String? id,
    String? customerId,
    String? serviceProviderId,
    String? serviceCategory,
    String? title,
    String? description,
    String? status,
    double? proposedPrice,
    double? agreedPrice,
    String? customerAddress,
    double? customerLatitude,
    double? customerLongitude,
    DateTime? requestedDate,
    DateTime? scheduledDate,
    DateTime? completedDate,
    List<String>? imageUrls,
    String? customerNotes,
    String? providerNotes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ServiceRequestModel(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      serviceProviderId: serviceProviderId ?? this.serviceProviderId,
      serviceCategory: serviceCategory ?? this.serviceCategory,
      title: title ?? this.title,
      description: description ?? this.description,
      status: status ?? this.status,
      proposedPrice: proposedPrice ?? this.proposedPrice,
      agreedPrice: agreedPrice ?? this.agreedPrice,
      customerAddress: customerAddress ?? this.customerAddress,
      customerLatitude: customerLatitude ?? this.customerLatitude,
      customerLongitude: customerLongitude ?? this.customerLongitude,
      requestedDate: requestedDate ?? this.requestedDate,
      scheduledDate: scheduledDate ?? this.scheduledDate,
      completedDate: completedDate ?? this.completedDate,
      imageUrls: imageUrls ?? this.imageUrls,
      customerNotes: customerNotes ?? this.customerNotes,
      providerNotes: providerNotes ?? this.providerNotes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
