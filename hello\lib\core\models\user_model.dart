import 'package:cloud_firestore/cloud_firestore.dart';

class UserModel {
  final String id;
  final String email;
  final String name;
  final String phone;
  final String userType; // customer, service_provider, admin
  final String? profileImageUrl;
  final String? address;
  final double? latitude;
  final double? longitude;
  final bool isActive;
  final bool isVerified;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Service Provider specific fields
  final String? businessName;
  final String? description;
  final List<String>? serviceCategories;
  final double? rating;
  final int? reviewCount;
  final List<String>? portfolioImages;
  final Map<String, dynamic>? workingHours;
  final bool? isAvailable;

  UserModel({
    required this.id,
    required this.email,
    required this.name,
    required this.phone,
    required this.userType,
    this.profileImageUrl,
    this.address,
    this.latitude,
    this.longitude,
    this.isActive = true,
    this.isVerified = false,
    required this.createdAt,
    required this.updatedAt,
    this.businessName,
    this.description,
    this.serviceCategories,
    this.rating,
    this.reviewCount,
    this.portfolioImages,
    this.workingHours,
    this.isAvailable,
  });

  factory UserModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return UserModel(
      id: doc.id,
      email: data['email'] ?? '',
      name: data['name'] ?? '',
      phone: data['phone'] ?? '',
      userType: data['userType'] ?? 'customer',
      profileImageUrl: data['profileImageUrl'],
      address: data['address'],
      latitude: data['latitude']?.toDouble(),
      longitude: data['longitude']?.toDouble(),
      isActive: data['isActive'] ?? true,
      isVerified: data['isVerified'] ?? false,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      businessName: data['businessName'],
      description: data['description'],
      serviceCategories: data['serviceCategories'] != null
          ? List<String>.from(data['serviceCategories'])
          : null,
      rating: data['rating']?.toDouble(),
      reviewCount: data['reviewCount'],
      portfolioImages: data['portfolioImages'] != null
          ? List<String>.from(data['portfolioImages'])
          : null,
      workingHours: data['workingHours'],
      isAvailable: data['isAvailable'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'email': email,
      'name': name,
      'phone': phone,
      'userType': userType,
      'profileImageUrl': profileImageUrl,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'isActive': isActive,
      'isVerified': isVerified,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'businessName': businessName,
      'description': description,
      'serviceCategories': serviceCategories,
      'rating': rating,
      'reviewCount': reviewCount,
      'portfolioImages': portfolioImages,
      'workingHours': workingHours,
      'isAvailable': isAvailable,
    };
  }

  UserModel copyWith({
    String? id,
    String? email,
    String? name,
    String? phone,
    String? userType,
    String? profileImageUrl,
    String? address,
    double? latitude,
    double? longitude,
    bool? isActive,
    bool? isVerified,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? businessName,
    String? description,
    List<String>? serviceCategories,
    double? rating,
    int? reviewCount,
    List<String>? portfolioImages,
    Map<String, dynamic>? workingHours,
    bool? isAvailable,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      userType: userType ?? this.userType,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      address: address ?? this.address,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      isActive: isActive ?? this.isActive,
      isVerified: isVerified ?? this.isVerified,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      businessName: businessName ?? this.businessName,
      description: description ?? this.description,
      serviceCategories: serviceCategories ?? this.serviceCategories,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      portfolioImages: portfolioImages ?? this.portfolioImages,
      workingHours: workingHours ?? this.workingHours,
      isAvailable: isAvailable ?? this.isAvailable,
    );
  }
}
