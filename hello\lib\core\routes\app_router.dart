import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:get_it/get_it.dart';

import '../../features/auth/providers/auth_provider.dart' as auth_provider;
import '../../features/auth/screens/login_screen.dart';
import '../../features/auth/screens/register_screen.dart';
import '../../features/auth/screens/user_type_selection_screen.dart';
import '../../features/home/<USER>/home_screen.dart';
import '../../features/services/screens/service_categories_screen.dart';
import '../../features/services/screens/service_providers_screen.dart';
import '../../features/services/screens/service_provider_profile_screen.dart';
import '../../features/services/screens/create_request_screen.dart';
import '../../features/services/screens/request_details_screen.dart';
import '../../features/services/screens/my_requests_screen.dart';
import '../../features/profile/screens/profile_screen.dart';
import '../../features/profile/screens/edit_profile_screen.dart';
import '../../features/admin/screens/admin_dashboard_screen.dart';

class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: '/',
    redirect: (context, state) {
      final authProvider = GetIt.instance<auth_provider.AuthProvider>();
      final isLoggedIn = authProvider.isAuthenticated;
      final currentUser = authProvider.currentUser;

      // If not logged in and trying to access protected routes
      if (!isLoggedIn && !_isPublicRoute(state.matchedLocation)) {
        return '/login';
      }

      // If logged in and trying to access auth routes
      if (isLoggedIn && _isAuthRoute(state.matchedLocation)) {
        if (currentUser?.userType == 'admin') {
          return '/admin';
        }
        return '/home';
      }

      return null;
    },
    routes: [
      // Auth Routes
      GoRoute(
        path: '/',
        builder: (context, state) => const UserTypeSelectionScreen(),
      ),
      GoRoute(path: '/login', builder: (context, state) => const LoginScreen()),
      GoRoute(
        path: '/register',
        builder: (context, state) {
          final userType = state.uri.queryParameters['userType'] ?? 'customer';
          return RegisterScreen(userType: userType);
        },
      ),

      // Main App Routes
      GoRoute(path: '/home', builder: (context, state) => const HomeScreen()),

      // Service Routes
      GoRoute(
        path: '/services',
        builder: (context, state) => const ServiceCategoriesScreen(),
      ),
      GoRoute(
        path: '/services/:category',
        builder: (context, state) {
          final category = state.pathParameters['category']!;
          return ServiceProvidersScreen(category: category);
        },
      ),
      GoRoute(
        path: '/provider/:providerId',
        builder: (context, state) {
          final providerId = state.pathParameters['providerId']!;
          return ServiceProviderProfileScreen(providerId: providerId);
        },
      ),
      GoRoute(
        path: '/create-request',
        builder: (context, state) {
          final category = state.uri.queryParameters['category'];
          final providerId = state.uri.queryParameters['providerId'];
          return CreateRequestScreen(
            category: category,
            providerId: providerId,
          );
        },
      ),
      GoRoute(
        path: '/request/:requestId',
        builder: (context, state) {
          final requestId = state.pathParameters['requestId']!;
          return RequestDetailsScreen(requestId: requestId);
        },
      ),
      GoRoute(
        path: '/my-requests',
        builder: (context, state) => const MyRequestsScreen(),
      ),

      // Profile Routes
      GoRoute(
        path: '/profile',
        builder: (context, state) => const ProfileScreen(),
      ),
      GoRoute(
        path: '/edit-profile',
        builder: (context, state) => const EditProfileScreen(),
      ),

      // Admin Routes
      GoRoute(
        path: '/admin',
        builder: (context, state) => const AdminDashboardScreen(),
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              'صفحة غير موجودة',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'المسار: ${state.matchedLocation}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go('/home'),
              child: const Text('العودة للرئيسية'),
            ),
          ],
        ),
      ),
    ),
  );

  static bool _isPublicRoute(String location) {
    const publicRoutes = ['/', '/login', '/register'];
    return publicRoutes.contains(location);
  }

  static bool _isAuthRoute(String location) {
    const authRoutes = ['/', '/login', '/register'];
    return authRoutes.contains(location);
  }
}
