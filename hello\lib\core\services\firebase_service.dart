import 'dart:math' as math;
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get_it/get_it.dart';

import '../constants/app_constants.dart';
import '../models/user_model.dart';
import '../models/service_request_model.dart';
import '../models/review_model.dart';

class FirebaseService {
  final FirebaseFirestore _firestore = GetIt.instance<FirebaseFirestore>();

  // User operations
  Future<UserModel?> getUserById(String userId) async {
    try {
      final doc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .get();

      if (doc.exists) {
        return UserModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get user: $e');
    }
  }

  Future<void> createUser(UserModel user) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(user.id)
          .set(user.toFirestore());
    } catch (e) {
      throw Exception('Failed to create user: $e');
    }
  }

  Future<void> updateUser(UserModel user) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(user.id)
          .update(user.toFirestore());
    } catch (e) {
      throw Exception('Failed to update user: $e');
    }
  }

  // Service Provider operations
  Future<List<UserModel>> getServiceProviders({
    String? category,
    double? latitude,
    double? longitude,
    double? radiusKm,
  }) async {
    try {
      Query query = _firestore
          .collection(AppConstants.usersCollection)
          .where('userType', isEqualTo: AppConstants.userTypeServiceProvider)
          .where('isActive', isEqualTo: true)
          .where('isVerified', isEqualTo: true);

      if (category != null) {
        query = query.where('serviceCategories', arrayContains: category);
      }

      final querySnapshot = await query.get();

      List<UserModel> providers = querySnapshot.docs
          .map((doc) => UserModel.fromFirestore(doc))
          .toList();

      // Filter by location if provided
      if (latitude != null && longitude != null && radiusKm != null) {
        providers = providers.where((provider) {
          if (provider.latitude == null || provider.longitude == null) {
            return false;
          }

          final distance = _calculateDistance(
            latitude,
            longitude,
            provider.latitude!,
            provider.longitude!,
          );

          return distance <= radiusKm;
        }).toList();
      }

      return providers;
    } catch (e) {
      throw Exception('Failed to get service providers: $e');
    }
  }

  // Service Request operations
  Future<String> createServiceRequest(ServiceRequestModel request) async {
    try {
      final docRef = await _firestore
          .collection(AppConstants.requestsCollection)
          .add(request.toFirestore());
      return docRef.id;
    } catch (e) {
      throw Exception('Failed to create service request: $e');
    }
  }

  Future<void> updateServiceRequest(ServiceRequestModel request) async {
    try {
      await _firestore
          .collection(AppConstants.requestsCollection)
          .doc(request.id)
          .update(request.toFirestore());
    } catch (e) {
      throw Exception('Failed to update service request: $e');
    }
  }

  Future<List<ServiceRequestModel>> getServiceRequests({
    String? customerId,
    String? serviceProviderId,
    String? status,
  }) async {
    try {
      Query query = _firestore.collection(AppConstants.requestsCollection);

      if (customerId != null) {
        query = query.where('customerId', isEqualTo: customerId);
      }

      if (serviceProviderId != null) {
        query = query.where('serviceProviderId', isEqualTo: serviceProviderId);
      }

      if (status != null) {
        query = query.where('status', isEqualTo: status);
      }

      query = query.orderBy('createdAt', descending: true);

      final querySnapshot = await query.get();

      return querySnapshot.docs
          .map((doc) => ServiceRequestModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get service requests: $e');
    }
  }

  // Review operations
  Future<String> createReview(ReviewModel review) async {
    try {
      final docRef = await _firestore
          .collection(AppConstants.reviewsCollection)
          .add(review.toFirestore());

      // Update service provider's rating
      await _updateProviderRating(review.serviceProviderId);

      return docRef.id;
    } catch (e) {
      throw Exception('Failed to create review: $e');
    }
  }

  Future<List<ReviewModel>> getReviews({
    String? serviceProviderId,
    String? customerId,
  }) async {
    try {
      Query query = _firestore.collection(AppConstants.reviewsCollection);

      if (serviceProviderId != null) {
        query = query.where('serviceProviderId', isEqualTo: serviceProviderId);
      }

      if (customerId != null) {
        query = query.where('customerId', isEqualTo: customerId);
      }

      query = query.orderBy('createdAt', descending: true);

      final querySnapshot = await query.get();

      return querySnapshot.docs
          .map((doc) => ReviewModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get reviews: $e');
    }
  }

  // Private helper methods
  Future<void> _updateProviderRating(String providerId) async {
    try {
      final reviews = await getReviews(serviceProviderId: providerId);

      if (reviews.isNotEmpty) {
        final totalRating = reviews.fold<double>(
          0.0,
          (total, review) => total + review.rating,
        );
        final averageRating = totalRating / reviews.length;

        await _firestore
            .collection(AppConstants.usersCollection)
            .doc(providerId)
            .update({'rating': averageRating, 'reviewCount': reviews.length});
      }
    } catch (e) {
      // Log error but don't throw to avoid breaking the review creation
      // TODO: Use proper logging instead of print
    }
  }

  double _calculateDistance(
    double lat1,
    double lon1,
    double lat2,
    double lon2,
  ) {
    // Haversine formula for calculating distance between two points
    const double earthRadius = 6371; // Earth's radius in kilometers

    final double dLat = _degreesToRadians(lat2 - lat1);
    final double dLon = _degreesToRadians(lon2 - lon1);

    final double a =
        math.sin(dLat / 2) * math.sin(dLat / 2) +
        math.cos(_degreesToRadians(lat1)) *
            math.cos(_degreesToRadians(lat2)) *
            math.sin(dLon / 2) *
            math.sin(dLon / 2);

    final double c = 2 * math.asin(math.sqrt(a));

    return earthRadius * c;
  }

  double _degreesToRadians(double degrees) {
    return degrees * (3.14159265359 / 180);
  }
}
