import 'package:get_it/get_it.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../features/auth/providers/auth_provider.dart' as auth_provider;
import '../../features/services/providers/services_provider.dart';
import '../../features/location/providers/location_provider.dart';
import '../services/firebase_service.dart';
import '../services/storage_service.dart';
import '../services/location_service.dart';

final GetIt getIt = GetIt.instance;

Future<void> setupServiceLocator() async {
  // External dependencies
  final sharedPreferences = await SharedPreferences.getInstance();
  getIt.registerSingleton<SharedPreferences>(sharedPreferences);

  getIt.registerSingleton<FirebaseAuth>(FirebaseAuth.instance);
  getIt.registerSingleton<FirebaseFirestore>(FirebaseFirestore.instance);
  getIt.registerSingleton<FirebaseStorage>(FirebaseStorage.instance);

  // Core services
  getIt.registerSingleton<FirebaseService>(FirebaseService());
  getIt.registerSingleton<StorageService>(StorageService());
  getIt.registerSingleton<LocationService>(LocationService());

  // Providers
  getIt.registerSingleton<auth_provider.AuthProvider>(
    auth_provider.AuthProvider(),
  );
  getIt.registerSingleton<ServicesProvider>(ServicesProvider());
  getIt.registerSingleton<LocationProvider>(LocationProvider());
}
