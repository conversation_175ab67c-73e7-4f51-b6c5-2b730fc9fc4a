import 'dart:io';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:get_it/get_it.dart';
import 'package:uuid/uuid.dart';

import '../constants/app_constants.dart';

class StorageService {
  final FirebaseStorage _storage = GetIt.instance<FirebaseStorage>();
  final Uuid _uuid = const Uuid();

  Future<String> uploadProfileImage(File imageFile, String userId) async {
    try {
      final String fileName = '${userId}_${_uuid.v4()}.jpg';
      final Reference ref = _storage
          .ref()
          .child(AppConstants.profileImagesPath)
          .child(fileName);

      final UploadTask uploadTask = ref.putFile(imageFile);
      final TaskSnapshot snapshot = await uploadTask;

      return await snapshot.ref.getDownloadURL();
    } catch (e) {
      throw Exception('Failed to upload profile image: $e');
    }
  }

  Future<String> uploadServiceImage(File imageFile, String requestId) async {
    try {
      final String fileName = '${requestId}_${_uuid.v4()}.jpg';
      final Reference ref = _storage
          .ref()
          .child(AppConstants.serviceImagesPath)
          .child(fileName);

      final UploadTask uploadTask = ref.putFile(imageFile);
      final TaskSnapshot snapshot = await uploadTask;

      return await snapshot.ref.getDownloadURL();
    } catch (e) {
      throw Exception('Failed to upload service image: $e');
    }
  }

  Future<List<String>> uploadServiceImages(
    List<File> imageFiles,
    String requestId,
  ) async {
    try {
      final List<String> downloadUrls = [];

      for (final File imageFile in imageFiles) {
        final String url = await uploadServiceImage(imageFile, requestId);
        downloadUrls.add(url);
      }

      return downloadUrls;
    } catch (e) {
      throw Exception('Failed to upload service images: $e');
    }
  }

  Future<String> uploadPortfolioImage(File imageFile, String providerId) async {
    try {
      final String fileName = '${providerId}_${_uuid.v4()}.jpg';
      final Reference ref = _storage
          .ref()
          .child(AppConstants.portfolioImagesPath)
          .child(fileName);

      final UploadTask uploadTask = ref.putFile(imageFile);
      final TaskSnapshot snapshot = await uploadTask;

      return await snapshot.ref.getDownloadURL();
    } catch (e) {
      throw Exception('Failed to upload portfolio image: $e');
    }
  }

  Future<List<String>> uploadPortfolioImages(
    List<File> imageFiles,
    String providerId,
  ) async {
    try {
      final List<String> downloadUrls = [];

      for (final File imageFile in imageFiles) {
        final String url = await uploadPortfolioImage(imageFile, providerId);
        downloadUrls.add(url);
      }

      return downloadUrls;
    } catch (e) {
      throw Exception('Failed to upload portfolio images: $e');
    }
  }

  Future<void> deleteImage(String imageUrl) async {
    try {
      final Reference ref = _storage.refFromURL(imageUrl);
      await ref.delete();
    } catch (e) {
      throw Exception('Failed to delete image: $e');
    }
  }

  Future<void> deleteImages(List<String> imageUrls) async {
    try {
      for (final String imageUrl in imageUrls) {
        await deleteImage(imageUrl);
      }
    } catch (e) {
      throw Exception('Failed to delete images: $e');
    }
  }
}
