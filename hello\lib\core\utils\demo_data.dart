import '../models/user_model.dart';
import '../constants/app_constants.dart';

class DemoData {
  // Demo users for testing
  static final List<Map<String, dynamic>> demoUsers = [
    {
      'email': '<EMAIL>',
      'password': '123456',
      'name': 'أحم<PERSON> محمد',
      'phone': '+***********',
      'userType': AppConstants.userTypeCustomer,
      'address': 'نواكشوط، موريتانيا',
    },
    {
      'email': '<EMAIL>',
      'password': '123456',
      'name': 'فاطمة أحمد',
      'phone': '+***********',
      'userType': AppConstants.userTypeServiceProvider,
      'businessName': 'خدمات التنظيف المنزلي',
      'description': 'نقدم خدمات التنظيف المنزلي والمكتبي بجودة عالية',
      'serviceCategories': ['تنظيف منزلي', 'تنظيف مكاتب'],
      'address': 'نواكشوط، موريتانيا',
      'portfolioImages': [
        'https://images.unsplash.com/photo-1581578731548-c64695cc6952?w=400',
        'https://images.unsplash.com/photo-1527515637462-cff94eecc1ac?w=400',
        'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=400',
        'https://images.unsplash.com/photo-1584622650111-993a426fbf0a?w=400',
      ],
    },
    {
      'email': '<EMAIL>',
      'password': '123456',
      'name': 'مدير النظام',
      'phone': '+***********',
      'userType': AppConstants.userTypeAdmin,
    },
    {
      'email': '<EMAIL>',
      'password': '123456',
      'name': 'المدير العام',
      'phone': '+***********',
      'userType': AppConstants.userTypeSuperAdmin,
    },
  ];

  // Demo service categories
  static final List<String> serviceCategories = [
    'تنظيف منزلي',
    'تنظيف مكاتب',
    'صيانة كهربائية',
    'صيانة سباكة',
    'نقل وتوصيل',
    'طبخ وتحضير طعام',
    'حراسة وأمن',
    'تدريس خصوصي',
    'تصميم وديكور',
    'خدمات تقنية',
  ];

  // Demo service requests
  static final List<Map<String, dynamic>> demoServiceRequests = [
    {
      'title': 'تنظيف شقة سكنية',
      'description': 'أحتاج لتنظيف شقة من 3 غرف وصالة ومطبخ',
      'category': 'تنظيف منزلي',
      'budget': 5000.0,
      'location': 'حي النصر، نواكشوط',
      'urgency': 'عادي',
    },
    {
      'title': 'إصلاح مشكلة كهربائية',
      'description': 'انقطاع الكهرباء في غرفة النوم الرئيسية',
      'category': 'صيانة كهربائية',
      'budget': 2000.0,
      'location': 'حي الرياض، نواكشوط',
      'urgency': 'عاجل',
    },
  ];

  // Helper method to create demo user model
  static UserModel createDemoUser(Map<String, dynamic> userData, String uid) {
    final now = DateTime.now();
    return UserModel(
      id: uid,
      email: userData['email'],
      name: userData['name'],
      phone: userData['phone'],
      userType: userData['userType'],
      isActive: true,
      isVerified: true,
      createdAt: now,
      updatedAt: now,
      address: userData['address'],
      businessName: userData['businessName'],
      description: userData['description'],
      serviceCategories: userData['serviceCategories']?.cast<String>(),
      isAvailable: userData['userType'] == AppConstants.userTypeServiceProvider
          ? true
          : null,
      rating: userData['userType'] == AppConstants.userTypeServiceProvider
          ? 4.5
          : null,
      reviewCount: userData['userType'] == AppConstants.userTypeServiceProvider
          ? 12
          : null,
    );
  }
}
