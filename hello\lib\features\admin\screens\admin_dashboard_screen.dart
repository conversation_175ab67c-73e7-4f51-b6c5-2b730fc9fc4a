import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/models/user_model.dart';
import '../../../core/models/service_request_model.dart';
import '../../auth/providers/auth_provider.dart';
import '../widgets/admin_stats_card.dart';

class AdminDashboardScreen extends StatefulWidget {
  const AdminDashboardScreen({super.key});

  @override
  State<AdminDashboardScreen> createState() => _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends State<AdminDashboardScreen> {
  Map<String, int> _stats = {};
  List<UserModel> _recentUsers = [];
  List<ServiceRequestModel> _recentRequests = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    try {
      setState(() => _isLoading = true);

      // Load statistics
      final usersSnapshot = await FirebaseFirestore.instance
          .collection(AppConstants.usersCollection)
          .get();

      final requestsSnapshot = await FirebaseFirestore.instance
          .collection(AppConstants.requestsCollection)
          .get();

      final reviewsSnapshot = await FirebaseFirestore.instance
          .collection(AppConstants.reviewsCollection)
          .get();

      // Calculate stats
      final users = usersSnapshot.docs
          .map((doc) => UserModel.fromFirestore(doc))
          .toList();

      final requests = requestsSnapshot.docs
          .map((doc) => ServiceRequestModel.fromFirestore(doc))
          .toList();

      _stats = {
        'totalUsers': users.length,
        'customers': users
            .where((u) => u.userType == AppConstants.userTypeCustomer)
            .length,
        'serviceProviders': users
            .where((u) => u.userType == AppConstants.userTypeServiceProvider)
            .length,
        'activeUsers': users.where((u) => u.isActive).length,
        'verifiedProviders': users
            .where(
              (u) =>
                  u.userType == AppConstants.userTypeServiceProvider &&
                  u.isVerified,
            )
            .length,
        'totalRequests': requests.length,
        'pendingRequests': requests.where((r) => r.status == 'pending').length,
        'completedRequests': requests
            .where((r) => r.status == 'completed')
            .length,
        'totalReviews': reviewsSnapshot.docs.length,
      };

      // Get recent users (last 5)
      _recentUsers =
          users
              .where((u) => u.userType != AppConstants.userTypeSuperAdmin)
              .toList()
            ..sort((a, b) => b.createdAt.compareTo(a.createdAt));

      if (_recentUsers.length > 5) {
        _recentUsers = _recentUsers.take(5).toList();
      }

      // Get recent requests (last 5)
      _recentRequests = requests
        ..sort((a, b) => b.createdAt.compareTo(a.createdAt));

      if (_recentRequests.length > 5) {
        _recentRequests = _recentRequests.take(5).toList();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في تحميل البيانات: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = context.watch<AuthProvider>();
    final currentUser = authProvider.currentUser;

    // Check if user is admin
    if (currentUser?.userType != AppConstants.userTypeAdmin &&
        currentUser?.userType != AppConstants.userTypeSuperAdmin) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('غير مصرح'),
          backgroundColor: AppColors.error,
          foregroundColor: AppColors.white,
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.block, size: 80, color: AppColors.error),
              SizedBox(height: 16),
              Text(
                'ليس لديك صلاحية للوصول لهذه الصفحة',
                style: TextStyle(fontSize: 18),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('لوحة الإدارة'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
        actions: [
          IconButton(
            onPressed: _loadDashboardData,
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث البيانات',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadDashboardData,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Welcome Card
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Row(
                          children: [
                            CircleAvatar(
                              radius: 30,
                              backgroundColor: AppColors.primary,
                              child: const Icon(
                                Icons.admin_panel_settings,
                                color: AppColors.white,
                                size: 30,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'مرحباً ${currentUser?.name}',
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleLarge
                                        ?.copyWith(fontWeight: FontWeight.bold),
                                  ),
                                  Text(
                                    'مدير النظام',
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodyMedium
                                        ?.copyWith(
                                          color: AppColors.textSecondary,
                                        ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Statistics Section
                    Text(
                      'إحصائيات النظام',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Stats Grid
                    GridView.count(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      crossAxisCount: 2,
                      crossAxisSpacing: 16,
                      mainAxisSpacing: 16,
                      childAspectRatio: 1.5,
                      children: [
                        AdminStatsCard(
                          title: 'إجمالي المستخدمين',
                          value: _stats['totalUsers']?.toString() ?? '0',
                          icon: Icons.people,
                          color: Colors.blue,
                        ),
                        AdminStatsCard(
                          title: 'العملاء',
                          value: _stats['customers']?.toString() ?? '0',
                          icon: Icons.person,
                          color: Colors.green,
                        ),
                        AdminStatsCard(
                          title: 'مقدمو الخدمات',
                          value: _stats['serviceProviders']?.toString() ?? '0',
                          icon: Icons.work,
                          color: Colors.orange,
                        ),
                        AdminStatsCard(
                          title: 'المستخدمون النشطون',
                          value: _stats['activeUsers']?.toString() ?? '0',
                          icon: Icons.check_circle,
                          color: Colors.teal,
                        ),
                        AdminStatsCard(
                          title: 'إجمالي الطلبات',
                          value: _stats['totalRequests']?.toString() ?? '0',
                          icon: Icons.assignment,
                          color: Colors.purple,
                        ),
                        AdminStatsCard(
                          title: 'الطلبات المعلقة',
                          value: _stats['pendingRequests']?.toString() ?? '0',
                          icon: Icons.pending,
                          color: Colors.amber,
                        ),
                      ],
                    ),

                    const SizedBox(height: 32),

                    // Quick Actions
                    Text(
                      'الإجراءات السريعة',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    Row(
                      children: [
                        Expanded(
                          child: _QuickActionCard(
                            title: 'إدارة المستخدمين',
                            icon: Icons.people_alt,
                            color: Colors.blue,
                            onTap: () => _showComingSoon('إدارة المستخدمين'),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _QuickActionCard(
                            title: 'إدارة الطلبات',
                            icon: Icons.assignment,
                            color: Colors.green,
                            onTap: () => _showComingSoon('إدارة الطلبات'),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    Row(
                      children: [
                        Expanded(
                          child: _QuickActionCard(
                            title: 'التقارير',
                            icon: Icons.analytics,
                            color: Colors.purple,
                            onTap: () => _showComingSoon('التقارير'),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _QuickActionCard(
                            title: 'إعدادات النظام',
                            icon: Icons.settings,
                            color: Colors.orange,
                            onTap: () => _showComingSoon('إعدادات النظام'),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 32),

                    // Recent Users Section
                    if (_recentUsers.isNotEmpty) ...[
                      Text(
                        'المستخدمون الجدد',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),

                      Card(
                        child: ListView.separated(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: _recentUsers.length,
                          separatorBuilder: (context, index) => const Divider(),
                          itemBuilder: (context, index) {
                            final user = _recentUsers[index];
                            return ListTile(
                              leading: CircleAvatar(
                                backgroundColor: _getUserTypeColor(
                                  user.userType,
                                ),
                                child: Icon(
                                  _getUserTypeIcon(user.userType),
                                  color: AppColors.white,
                                ),
                              ),
                              title: Text(user.name),
                              subtitle: Text(user.email),
                              trailing: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Text(
                                    _getUserTypeLabel(user.userType),
                                    style: const TextStyle(fontSize: 12),
                                  ),
                                  Text(
                                    _formatDate(user.createdAt),
                                    style: TextStyle(
                                      fontSize: 10,
                                      color: AppColors.textSecondary,
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      ),

                      const SizedBox(height: 32),
                    ],

                    // Recent Requests Section
                    if (_recentRequests.isNotEmpty) ...[
                      Text(
                        'الطلبات الأخيرة',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),

                      Card(
                        child: ListView.separated(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: _recentRequests.length,
                          separatorBuilder: (context, index) => const Divider(),
                          itemBuilder: (context, index) {
                            final request = _recentRequests[index];
                            return ListTile(
                              leading: CircleAvatar(
                                backgroundColor: _getStatusColor(
                                  request.status,
                                ),
                                child: Icon(
                                  _getStatusIcon(request.status),
                                  color: AppColors.white,
                                ),
                              ),
                              title: Text(request.title),
                              subtitle: Text(
                                _getCategoryName(request.category),
                              ),
                              trailing: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Text(
                                    _getStatusText(request.status),
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: _getStatusColor(request.status),
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  Text(
                                    _formatDate(request.createdAt),
                                    style: TextStyle(
                                      fontSize: 10,
                                      color: AppColors.textSecondary,
                                    ),
                                  ),
                                ],
                              ),
                              onTap: () => context.go('/request/${request.id}'),
                            );
                          },
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
    );
  }

  void _showComingSoon(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$feature - قريباً'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  Color _getUserTypeColor(String userType) {
    switch (userType) {
      case AppConstants.userTypeCustomer:
        return Colors.green;
      case AppConstants.userTypeServiceProvider:
        return Colors.orange;
      case AppConstants.userTypeAdmin:
        return Colors.purple;
      default:
        return AppColors.textSecondary;
    }
  }

  IconData _getUserTypeIcon(String userType) {
    switch (userType) {
      case AppConstants.userTypeCustomer:
        return Icons.person;
      case AppConstants.userTypeServiceProvider:
        return Icons.work;
      case AppConstants.userTypeAdmin:
        return Icons.admin_panel_settings;
      default:
        return Icons.person;
    }
  }

  String _getUserTypeLabel(String userType) {
    switch (userType) {
      case AppConstants.userTypeCustomer:
        return 'عميل';
      case AppConstants.userTypeServiceProvider:
        return 'مقدم خدمة';
      case AppConstants.userTypeAdmin:
        return 'مدير';
      default:
        return 'غير محدد';
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'pending':
        return AppColors.warning;
      case 'accepted':
        return AppColors.info;
      case 'in_progress':
        return AppColors.primary;
      case 'completed':
        return AppColors.success;
      case 'cancelled':
        return AppColors.error;
      default:
        return AppColors.textSecondary;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'pending':
        return Icons.pending;
      case 'accepted':
        return Icons.check_circle;
      case 'in_progress':
        return Icons.work;
      case 'completed':
        return Icons.done_all;
      case 'cancelled':
        return Icons.cancel;
      default:
        return Icons.help;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'pending':
        return 'في الانتظار';
      case 'accepted':
        return 'مقبول';
      case 'in_progress':
        return 'قيد التنفيذ';
      case 'completed':
        return 'مكتمل';
      case 'cancelled':
        return 'ملغي';
      default:
        return 'غير محدد';
    }
  }

  String _getCategoryName(String category) {
    switch (category) {
      case 'plumbing':
        return 'السباكة';
      case 'carpentry':
        return 'النجارة';
      case 'electrical':
        return 'الكهرباء';
      case 'painting':
        return 'الدهان';
      case 'appliance_repair':
        return 'إصلاح الأجهزة';
      case 'furniture_moving':
        return 'نقل الأثاث';
      case 'gardening':
        return 'خدمات الحدائق';
      default:
        return category;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

class _QuickActionCard extends StatelessWidget {
  final String title;
  final IconData icon;
  final Color color;
  final VoidCallback onTap;

  const _QuickActionCard({
    required this.title,
    required this.icon,
    required this.color,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, size: 32, color: color),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
