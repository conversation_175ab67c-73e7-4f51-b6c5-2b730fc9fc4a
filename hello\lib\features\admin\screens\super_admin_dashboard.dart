import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/models/user_model.dart';
import '../../auth/providers/auth_provider.dart';
import '../widgets/admin_stats_card.dart';
import '../widgets/user_management_card.dart';
import '../widgets/system_settings_card.dart';

class SuperAdminDashboard extends StatefulWidget {
  const SuperAdminDashboard({super.key});

  @override
  State<SuperAdminDashboard> createState() => _SuperAdminDashboardState();
}

class _SuperAdminDashboardState extends State<SuperAdminDashboard> {
  Map<String, int> _stats = {};
  List<UserModel> _recentUsers = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    try {
      setState(() => _isLoading = true);

      // Load statistics
      final usersSnapshot = await FirebaseFirestore.instance
          .collection(AppConstants.usersCollection)
          .get();

      final requestsSnapshot = await FirebaseFirestore.instance
          .collection(AppConstants.requestsCollection)
          .get();

      final reviewsSnapshot = await FirebaseFirestore.instance
          .collection(AppConstants.reviewsCollection)
          .get();

      // Calculate stats
      final users = usersSnapshot.docs
          .map((doc) => UserModel.fromFirestore(doc))
          .toList();

      _stats = {
        'totalUsers': users.length,
        'customers': users
            .where((u) => u.userType == AppConstants.userTypeCustomer)
            .length,
        'serviceProviders': users
            .where((u) => u.userType == AppConstants.userTypeServiceProvider)
            .length,
        'admins': users
            .where((u) => u.userType == AppConstants.userTypeAdmin)
            .length,
        'superAdmins': users
            .where((u) => u.userType == AppConstants.userTypeSuperAdmin)
            .length,
        'activeUsers': users.where((u) => u.isActive).length,
        'verifiedProviders': users
            .where(
              (u) =>
                  u.userType == AppConstants.userTypeServiceProvider &&
                  u.isVerified,
            )
            .length,
        'totalRequests': requestsSnapshot.docs.length,
        'totalReviews': reviewsSnapshot.docs.length,
      };

      // Get recent users (last 10)
      _recentUsers =
          users
              .where((u) => u.userType != AppConstants.userTypeSuperAdmin)
              .toList()
            ..sort((a, b) => b.createdAt.compareTo(a.createdAt));

      if (_recentUsers.length > 10) {
        _recentUsers = _recentUsers.take(10).toList();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في تحميل البيانات: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = context.watch<AuthProvider>();
    final currentUser = authProvider.currentUser;

    // Check if user is super admin
    if (currentUser?.userType != AppConstants.userTypeSuperAdmin) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('غير مصرح'),
          backgroundColor: AppColors.error,
          foregroundColor: Colors.white,
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.block, size: 80, color: AppColors.error),
              SizedBox(height: 16),
              Text(
                'ليس لديك صلاحية للوصول لهذه الصفحة',
                style: TextStyle(fontSize: 18),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('لوحة تحكم المدير العام'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _loadDashboardData,
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث البيانات',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadDashboardData,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Welcome Card
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Row(
                          children: [
                            CircleAvatar(
                              radius: 30,
                              backgroundColor: AppColors.primary,
                              child: const Icon(
                                Icons.admin_panel_settings,
                                color: Colors.white,
                                size: 30,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'مرحباً ${currentUser?.name}',
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleLarge
                                        ?.copyWith(fontWeight: FontWeight.bold),
                                  ),
                                  Text(
                                    'المدير العام للنظام',
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodyMedium
                                        ?.copyWith(color: Colors.grey[600]),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Statistics Section
                    Text(
                      'إحصائيات النظام',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Stats Grid
                    GridView.count(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      crossAxisCount: 2,
                      crossAxisSpacing: 16,
                      mainAxisSpacing: 16,
                      childAspectRatio: 1.5,
                      children: [
                        AdminStatsCard(
                          title: 'إجمالي المستخدمين',
                          value: _stats['totalUsers']?.toString() ?? '0',
                          icon: Icons.people,
                          color: Colors.blue,
                        ),
                        AdminStatsCard(
                          title: 'العملاء',
                          value: _stats['customers']?.toString() ?? '0',
                          icon: Icons.person,
                          color: Colors.green,
                        ),
                        AdminStatsCard(
                          title: 'مقدمو الخدمات',
                          value: _stats['serviceProviders']?.toString() ?? '0',
                          icon: Icons.work,
                          color: Colors.orange,
                        ),
                        AdminStatsCard(
                          title: 'المديرون',
                          value: _stats['admins']?.toString() ?? '0',
                          icon: Icons.admin_panel_settings,
                          color: Colors.purple,
                        ),
                        AdminStatsCard(
                          title: 'المستخدمون النشطون',
                          value: _stats['activeUsers']?.toString() ?? '0',
                          icon: Icons.check_circle,
                          color: Colors.teal,
                        ),
                        AdminStatsCard(
                          title: 'مقدمو خدمات معتمدون',
                          value: _stats['verifiedProviders']?.toString() ?? '0',
                          icon: Icons.verified,
                          color: Colors.indigo,
                        ),
                      ],
                    ),

                    const SizedBox(height: 32),

                    // Management Section
                    Text(
                      'إدارة النظام',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Management Cards
                    UserManagementCard(
                      onUsersManagement: () => _navigateToUsersManagement(),
                      onAdminsManagement: () => _navigateToAdminsManagement(),
                      onReportsManagement: () => _navigateToReportsManagement(),
                    ),

                    const SizedBox(height: 16),

                    SystemSettingsCard(
                      onSystemSettings: () => _navigateToSystemSettings(),
                      onBackupRestore: () => _navigateToBackupRestore(),
                      onSecuritySettings: () => _navigateToSecuritySettings(),
                    ),

                    const SizedBox(height: 32),

                    // Recent Users Section
                    if (_recentUsers.isNotEmpty) ...[
                      Text(
                        'المستخدمون الجدد',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),

                      Card(
                        child: ListView.separated(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: _recentUsers.length,
                          separatorBuilder: (context, index) => const Divider(),
                          itemBuilder: (context, index) {
                            final user = _recentUsers[index];
                            return ListTile(
                              leading: CircleAvatar(
                                backgroundColor: _getUserTypeColor(
                                  user.userType,
                                ),
                                child: Icon(
                                  _getUserTypeIcon(user.userType),
                                  color: Colors.white,
                                ),
                              ),
                              title: Text(user.name),
                              subtitle: Text(user.email),
                              trailing: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Text(
                                    _getUserTypeLabel(user.userType),
                                    style: const TextStyle(fontSize: 12),
                                  ),
                                  Text(
                                    _formatDate(user.createdAt),
                                    style: TextStyle(
                                      fontSize: 10,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
    );
  }

  Color _getUserTypeColor(String userType) {
    switch (userType) {
      case AppConstants.userTypeCustomer:
        return Colors.green;
      case AppConstants.userTypeServiceProvider:
        return Colors.orange;
      case AppConstants.userTypeAdmin:
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  IconData _getUserTypeIcon(String userType) {
    switch (userType) {
      case AppConstants.userTypeCustomer:
        return Icons.person;
      case AppConstants.userTypeServiceProvider:
        return Icons.work;
      case AppConstants.userTypeAdmin:
        return Icons.admin_panel_settings;
      default:
        return Icons.person;
    }
  }

  String _getUserTypeLabel(String userType) {
    switch (userType) {
      case AppConstants.userTypeCustomer:
        return 'عميل';
      case AppConstants.userTypeServiceProvider:
        return 'مقدم خدمة';
      case AppConstants.userTypeAdmin:
        return 'مدير';
      default:
        return 'غير محدد';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _navigateToUsersManagement() {
    // TODO: Navigate to users management screen
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('إدارة المستخدمين - قريباً')));
  }

  void _navigateToAdminsManagement() {
    // TODO: Navigate to admins management screen
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('إدارة المديرين - قريباً')));
  }

  void _navigateToReportsManagement() {
    // TODO: Navigate to reports management screen
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('إدارة التقارير - قريباً')));
  }

  void _navigateToSystemSettings() {
    // TODO: Navigate to system settings screen
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('إعدادات النظام - قريباً')));
  }

  void _navigateToBackupRestore() {
    // TODO: Navigate to backup/restore screen
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('النسخ الاحتياطي - قريباً')));
  }

  void _navigateToSecuritySettings() {
    // TODO: Navigate to security settings screen
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('إعدادات الأمان - قريباً')));
  }
}
