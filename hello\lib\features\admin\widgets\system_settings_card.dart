import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';

class SystemSettingsCard extends StatelessWidget {
  final VoidCallback onSystemSettings;
  final VoidCallback onBackupRestore;
  final VoidCallback onSecuritySettings;

  const SystemSettingsCard({
    super.key,
    required this.onSystemSettings,
    required this.onBackupRestore,
    required this.onSecuritySettings,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.settings, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  'إعدادات النظام',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Settings Options
            _buildSettingsOption(
              context,
              icon: Icons.tune,
              title: 'إعدادات عامة',
              subtitle: 'إعدادات التطبيق العامة',
              onTap: onSystemSettings,
            ),
            const SizedBox(height: 12),

            _buildSettingsOption(
              context,
              icon: Icons.backup,
              title: 'النسخ الاحتياطي',
              subtitle: 'إنشاء واستعادة النسخ الاحتياطية',
              onTap: onBackupRestore,
            ),
            const SizedBox(height: 12),

            _buildSettingsOption(
              context,
              icon: Icons.security,
              title: 'إعدادات الأمان',
              subtitle: 'إدارة أمان النظام',
              onTap: onSecuritySettings,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsOption(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: AppColors.primary),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
            Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey[400]),
          ],
        ),
      ),
    );
  }
}
