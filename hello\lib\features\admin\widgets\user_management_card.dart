import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';

class UserManagementCard extends StatelessWidget {
  final VoidCallback onUsersManagement;
  final VoidCallback onAdminsManagement;
  final VoidCallback onReportsManagement;

  const UserManagementCard({
    super.key,
    required this.onUsersManagement,
    required this.onAdminsManagement,
    required this.onReportsManagement,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.manage_accounts, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  'إدارة المستخدمين',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Management Options
            _buildManagementOption(
              context,
              icon: Icons.people,
              title: 'إدارة المستخدمين',
              subtitle: 'عرض وإدارة جميع المستخدمين',
              onTap: onUsersManagement,
            ),
            const SizedBox(height: 12),

            _buildManagementOption(
              context,
              icon: Icons.admin_panel_settings,
              title: 'إدارة المديرين',
              subtitle: 'إضافة وإدارة المديرين',
              onTap: onAdminsManagement,
            ),
            const SizedBox(height: 12),

            _buildManagementOption(
              context,
              icon: Icons.report,
              title: 'التقارير والشكاوى',
              subtitle: 'مراجعة التقارير والشكاوى',
              onTap: onReportsManagement,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildManagementOption(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: AppColors.primary),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
            Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey[400]),
          ],
        ),
      ),
    );
  }
}
