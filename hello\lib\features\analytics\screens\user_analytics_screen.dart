import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fl_chart/fl_chart.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/models/service_request_model.dart';
import '../../../core/models/review_model.dart';
import '../../auth/providers/auth_provider.dart';

class UserAnalyticsScreen extends StatefulWidget {
  const UserAnalyticsScreen({super.key});

  @override
  State<UserAnalyticsScreen> createState() => _UserAnalyticsScreenState();
}

class _UserAnalyticsScreenState extends State<UserAnalyticsScreen> {
  Map<String, dynamic> _analytics = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAnalytics();
  }

  Future<void> _loadAnalytics() async {
    try {
      setState(() => _isLoading = true);

      final authProvider = context.read<AuthProvider>();
      final currentUser = authProvider.currentUser;

      if (currentUser == null) return;

      final analytics = <String, dynamic>{};

      if (currentUser.userType == AppConstants.userTypeServiceProvider) {
        // Service Provider Analytics
        await _loadServiceProviderAnalytics(currentUser.id, analytics);
      } else if (currentUser.userType == AppConstants.userTypeCustomer) {
        // Customer Analytics
        await _loadCustomerAnalytics(currentUser.id, analytics);
      }

      setState(() {
        _analytics = analytics;
        _isLoading = false;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في تحميل الإحصائيات: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadServiceProviderAnalytics(
    String providerId,
    Map<String, dynamic> analytics,
  ) async {
    // Load requests
    final requestsSnapshot = await FirebaseFirestore.instance
        .collection(AppConstants.requestsCollection)
        .where('serviceProviderId', isEqualTo: providerId)
        .get();

    final requests = requestsSnapshot.docs
        .map((doc) => ServiceRequestModel.fromFirestore(doc))
        .toList();

    // Load reviews
    final reviewsSnapshot = await FirebaseFirestore.instance
        .collection(AppConstants.reviewsCollection)
        .where('serviceProviderId', isEqualTo: providerId)
        .get();

    final reviews = reviewsSnapshot.docs
        .map((doc) => ReviewModel.fromFirestore(doc))
        .toList();

    // Calculate analytics
    analytics['totalRequests'] = requests.length;
    analytics['completedRequests'] = requests
        .where((r) => r.status == 'completed')
        .length;
    analytics['pendingRequests'] = requests
        .where((r) => r.status == 'pending')
        .length;
    analytics['inProgressRequests'] = requests
        .where((r) => r.status == 'in_progress')
        .length;
    analytics['cancelledRequests'] = requests
        .where((r) => r.status == 'cancelled')
        .length;

    analytics['totalReviews'] = reviews.length;
    analytics['averageRating'] = reviews.isEmpty
        ? 0.0
        : reviews.map((r) => r.rating).reduce((a, b) => a + b) / reviews.length;

    analytics['totalEarnings'] = requests
        .where((r) => r.status == 'completed' && r.finalPrice != null)
        .map((r) => r.finalPrice!)
        .fold(0.0, (sum, price) => sum + price);

    // Monthly data for charts
    final now = DateTime.now();
    final monthlyData = <String, int>{};
    final monthlyEarnings = <String, double>{};

    for (int i = 5; i >= 0; i--) {
      final month = DateTime(now.year, now.month - i, 1);
      final monthKey = '${month.month}/${month.year}';

      final monthRequests = requests
          .where(
            (r) =>
                r.createdAt.year == month.year &&
                r.createdAt.month == month.month,
          )
          .length;

      final monthEarning = requests
          .where(
            (r) =>
                r.createdAt.year == month.year &&
                r.createdAt.month == month.month &&
                r.status == 'completed' &&
                r.finalPrice != null,
          )
          .map((r) => r.finalPrice!)
          .fold(0.0, (sum, price) => sum + price);

      monthlyData[monthKey] = monthRequests;
      monthlyEarnings[monthKey] = monthEarning;
    }

    analytics['monthlyRequests'] = monthlyData;
    analytics['monthlyEarnings'] = monthlyEarnings;

    // Category breakdown
    final categoryData = <String, int>{};
    for (final request in requests) {
      categoryData[request.category] =
          (categoryData[request.category] ?? 0) + 1;
    }
    analytics['categoryBreakdown'] = categoryData;
  }

  Future<void> _loadCustomerAnalytics(
    String customerId,
    Map<String, dynamic> analytics,
  ) async {
    // Load requests
    final requestsSnapshot = await FirebaseFirestore.instance
        .collection(AppConstants.requestsCollection)
        .where('customerId', isEqualTo: customerId)
        .get();

    final requests = requestsSnapshot.docs
        .map((doc) => ServiceRequestModel.fromFirestore(doc))
        .toList();

    // Load reviews given by customer
    final reviewsSnapshot = await FirebaseFirestore.instance
        .collection(AppConstants.reviewsCollection)
        .where('customerId', isEqualTo: customerId)
        .get();

    final reviews = reviewsSnapshot.docs
        .map((doc) => ReviewModel.fromFirestore(doc))
        .toList();

    // Calculate analytics
    analytics['totalRequests'] = requests.length;
    analytics['completedRequests'] = requests
        .where((r) => r.status == 'completed')
        .length;
    analytics['pendingRequests'] = requests
        .where((r) => r.status == 'pending')
        .length;
    analytics['inProgressRequests'] = requests
        .where((r) => r.status == 'in_progress')
        .length;
    analytics['cancelledRequests'] = requests
        .where((r) => r.status == 'cancelled')
        .length;

    analytics['totalReviews'] = reviews.length;
    analytics['averageRatingGiven'] = reviews.isEmpty
        ? 0.0
        : reviews.map((r) => r.rating).reduce((a, b) => a + b) / reviews.length;

    analytics['totalSpent'] = requests
        .where((r) => r.status == 'completed' && r.finalPrice != null)
        .map((r) => r.finalPrice!)
        .fold(0.0, (sum, price) => sum + price);

    // Monthly data for charts
    final now = DateTime.now();
    final monthlyData = <String, int>{};
    final monthlySpending = <String, double>{};

    for (int i = 5; i >= 0; i--) {
      final month = DateTime(now.year, now.month - i, 1);
      final monthKey = '${month.month}/${month.year}';

      final monthRequests = requests
          .where(
            (r) =>
                r.createdAt.year == month.year &&
                r.createdAt.month == month.month,
          )
          .length;

      final monthSpent = requests
          .where(
            (r) =>
                r.createdAt.year == month.year &&
                r.createdAt.month == month.month &&
                r.status == 'completed' &&
                r.finalPrice != null,
          )
          .map((r) => r.finalPrice!)
          .fold(0.0, (sum, price) => sum + price);

      monthlyData[monthKey] = monthRequests;
      monthlySpending[monthKey] = monthSpent;
    }

    analytics['monthlyRequests'] = monthlyData;
    analytics['monthlySpending'] = monthlySpending;

    // Category breakdown
    final categoryData = <String, int>{};
    for (final request in requests) {
      categoryData[request.category] =
          (categoryData[request.category] ?? 0) + 1;
    }
    analytics['categoryBreakdown'] = categoryData;
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = context.watch<AuthProvider>();
    final currentUser = authProvider.currentUser;

    if (currentUser == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('الإحصائيات'),
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.white,
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    final isServiceProvider =
        currentUser.userType == AppConstants.userTypeServiceProvider;

    return Scaffold(
      appBar: AppBar(
        title: Text(isServiceProvider ? 'إحصائيات أعمالي' : 'إحصائياتي'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
        actions: [
          IconButton(
            onPressed: _loadAnalytics,
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث الإحصائيات',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadAnalytics,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Overview Cards
                    _buildOverviewCards(isServiceProvider),

                    const SizedBox(height: 24),

                    // Monthly Chart
                    _buildMonthlyChart(isServiceProvider),

                    const SizedBox(height: 24),

                    // Category Breakdown
                    _buildCategoryBreakdown(),

                    const SizedBox(height: 24),

                    // Additional Stats
                    _buildAdditionalStats(isServiceProvider),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildOverviewCards(bool isServiceProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نظرة عامة',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),

        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.5,
          children: [
            _StatCard(
              title: 'إجمالي الطلبات',
              value: _analytics['totalRequests']?.toString() ?? '0',
              icon: Icons.assignment,
              color: Colors.blue,
            ),
            _StatCard(
              title: 'الطلبات المكتملة',
              value: _analytics['completedRequests']?.toString() ?? '0',
              icon: Icons.check_circle,
              color: AppColors.success,
            ),
            _StatCard(
              title: 'الطلبات المعلقة',
              value: _analytics['pendingRequests']?.toString() ?? '0',
              icon: Icons.pending,
              color: AppColors.warning,
            ),
            _StatCard(
              title: isServiceProvider ? 'إجمالي الأرباح' : 'إجمالي المصروفات',
              value:
                  '${(isServiceProvider ? _analytics['totalEarnings'] : _analytics['totalSpent'])?.toStringAsFixed(0) ?? '0'} أوقية',
              icon: Icons.attach_money,
              color: isServiceProvider ? AppColors.success : AppColors.info,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMonthlyChart(bool isServiceProvider) {
    final monthlyData =
        _analytics['monthlyRequests'] as Map<String, int>? ?? {};

    if (monthlyData.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الطلبات الشهرية',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),

        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: SizedBox(
              height: 200,
              child: LineChart(
                LineChartData(
                  gridData: FlGridData(show: true),
                  titlesData: FlTitlesData(
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: true),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          final keys = monthlyData.keys.toList();
                          if (value.toInt() < keys.length) {
                            return Text(
                              keys[value.toInt()],
                              style: const TextStyle(fontSize: 10),
                            );
                          }
                          return const Text('');
                        },
                      ),
                    ),
                    topTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    rightTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                  ),
                  borderData: FlBorderData(show: true),
                  lineBarsData: [
                    LineChartBarData(
                      spots: monthlyData.values
                          .toList()
                          .asMap()
                          .entries
                          .map(
                            (entry) => FlSpot(
                              entry.key.toDouble(),
                              entry.value.toDouble(),
                            ),
                          )
                          .toList(),
                      isCurved: true,
                      color: AppColors.primary,
                      barWidth: 3,
                      dotData: FlDotData(show: true),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryBreakdown() {
    final categoryData =
        _analytics['categoryBreakdown'] as Map<String, int>? ?? {};

    if (categoryData.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'توزيع الطلبات حسب الفئة',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),

        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: categoryData.entries.map((entry) {
                final total = categoryData.values.fold(
                  0,
                  (sum, value) => sum + value,
                );
                final percentage = total > 0
                    ? (entry.value / total * 100)
                    : 0.0;

                return Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: Text(_getCategoryName(entry.key)),
                      ),
                      Expanded(
                        flex: 3,
                        child: LinearProgressIndicator(
                          value: percentage / 100,
                          backgroundColor: Colors.grey[300],
                          valueColor: AlwaysStoppedAnimation<Color>(
                            _getCategoryColor(entry.key),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '${entry.value} (${percentage.toStringAsFixed(1)}%)',
                        style: const TextStyle(fontSize: 12),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAdditionalStats(bool isServiceProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إحصائيات إضافية',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),

        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                _buildStatRow(
                  'إجمالي التقييمات',
                  _analytics['totalReviews']?.toString() ?? '0',
                  Icons.star,
                ),
                const Divider(),
                _buildStatRow(
                  isServiceProvider ? 'متوسط التقييم' : 'متوسط التقييم المُعطى',
                  '${(_analytics[isServiceProvider ? 'averageRating' : 'averageRatingGiven'] ?? 0.0).toStringAsFixed(1)} ⭐',
                  Icons.star_rate,
                ),
                const Divider(),
                _buildStatRow(
                  'الطلبات قيد التنفيذ',
                  _analytics['inProgressRequests']?.toString() ?? '0',
                  Icons.work,
                ),
                const Divider(),
                _buildStatRow(
                  'الطلبات الملغاة',
                  _analytics['cancelledRequests']?.toString() ?? '0',
                  Icons.cancel,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStatRow(String title, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: AppColors.primary),
        const SizedBox(width: 12),
        Expanded(child: Text(title)),
        Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
      ],
    );
  }

  String _getCategoryName(String category) {
    switch (category) {
      case 'plumbing':
        return 'السباكة';
      case 'carpentry':
        return 'النجارة';
      case 'electrical':
        return 'الكهرباء';
      case 'painting':
        return 'الدهان';
      case 'appliance_repair':
        return 'إصلاح الأجهزة';
      case 'furniture_moving':
        return 'نقل الأثاث';
      case 'gardening':
        return 'خدمات الحدائق';
      default:
        return category;
    }
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case 'plumbing':
        return Colors.blue;
      case 'carpentry':
        return Colors.brown;
      case 'electrical':
        return Colors.amber;
      case 'painting':
        return Colors.purple;
      case 'appliance_repair':
        return Colors.green;
      case 'furniture_moving':
        return Colors.orange;
      case 'gardening':
        return Colors.teal;
      default:
        return AppColors.primary;
    }
  }
}

class _StatCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;

  const _StatCard({
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
