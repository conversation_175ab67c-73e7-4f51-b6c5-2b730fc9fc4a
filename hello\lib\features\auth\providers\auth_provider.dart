import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:get_it/get_it.dart';

import '../../../core/models/user_model.dart';
import '../../../core/services/firebase_service.dart';
import '../../../core/constants/app_constants.dart';

class AuthProvider extends ChangeNotifier {
  final FirebaseAuth _auth = GetIt.instance<FirebaseAuth>();
  final FirebaseService _firebaseService = GetIt.instance<FirebaseService>();

  User? _firebaseUser;
  UserModel? _currentUser;
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  User? get firebaseUser => _firebaseUser;
  UserModel? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated => _firebaseUser != null && _currentUser != null;

  AuthProvider() {
    _init();
  }

  void _init() {
    _auth.authStateChanges().listen(_onAuthStateChanged);
  }

  Future<void> _onAuthStateChanged(User? user) async {
    _firebaseUser = user;

    if (user != null) {
      try {
        _currentUser = await _firebaseService.getUserById(user.uid);
        _errorMessage = null; // Clear any previous errors
      } catch (e) {
        if (kDebugMode) {
          print('Failed to get user: $e');
        }

        // Handle offline state
        if (e.toString().contains('client is offline') ||
            e.toString().contains('unavailable')) {
          _errorMessage =
              'لا يوجد اتصال بالإنترنت. يرجى التحقق من الاتصال والمحاولة مرة أخرى.';
        } else if (e.toString().contains('api-key-not-valid')) {
          _errorMessage =
              'إعدادات Firebase غير صحيحة. يرجى التحقق من الإعدادات.';
        } else {
          _errorMessage = 'فشل في تحميل بيانات المستخدم: $e';
        }
      }
    } else {
      _currentUser = null;
      _errorMessage = null;
    }

    notifyListeners();
  }

  Future<bool> signInWithEmailAndPassword(String email, String password) async {
    try {
      _setLoading(true);
      _clearError();

      final UserCredential result = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (result.user != null) {
        _currentUser = await _firebaseService.getUserById(result.user!.uid);
        return true;
      }

      return false;
    } on FirebaseAuthException catch (e) {
      _setError(_getAuthErrorMessage(e.code));
      return false;
    } catch (e) {
      if (kDebugMode) {
        print('Sign in error: $e');
      }

      // Handle specific Firebase errors
      if (e.toString().contains('client is offline') ||
          e.toString().contains('unavailable')) {
        _setError(
          'لا يوجد اتصال بالإنترنت. يرجى التحقق من الاتصال والمحاولة مرة أخرى.',
        );
      } else if (e.toString().contains('api-key-not-valid')) {
        _setError('إعدادات Firebase غير صحيحة. يرجى التحقق من الإعدادات.');
      } else {
        _setError('حدث خطأ في المصادقة: $e');
      }
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> registerWithEmailAndPassword({
    required String email,
    required String password,
    required String name,
    required String phone,
    required String userType,
    String? businessName,
    String? description,
    List<String>? serviceCategories,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      final UserCredential result = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (result.user != null) {
        final now = DateTime.now();
        final newUser = UserModel(
          id: result.user!.uid,
          email: email,
          name: name,
          phone: phone,
          userType: userType,
          isActive: true,
          isVerified:
              userType ==
              AppConstants.userTypeCustomer, // Auto-verify customers
          createdAt: now,
          updatedAt: now,
          businessName: businessName,
          description: description,
          serviceCategories: serviceCategories,
          isAvailable: userType == AppConstants.userTypeServiceProvider
              ? true
              : null,
        );

        await _firebaseService.createUser(newUser);
        _currentUser = newUser;

        return true;
      }

      return false;
    } on FirebaseAuthException catch (e) {
      _setError(_getAuthErrorMessage(e.code));
      return false;
    } catch (e) {
      _setError('حدث خطأ غير متوقع: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<void> signOut() async {
    try {
      await _auth.signOut();
      _currentUser = null;
      _firebaseUser = null;
      notifyListeners();
    } catch (e) {
      _setError('فشل في تسجيل الخروج: $e');
    }
  }

  Future<bool> updateProfile({
    String? name,
    String? phone,
    String? address,
    double? latitude,
    double? longitude,
    String? profileImageUrl,
    String? businessName,
    String? description,
    List<String>? serviceCategories,
    List<String>? portfolioImages,
    Map<String, dynamic>? workingHours,
    bool? isAvailable,
  }) async {
    try {
      if (_currentUser == null) return false;

      _setLoading(true);
      _clearError();

      final updatedUser = _currentUser!.copyWith(
        name: name,
        phone: phone,
        address: address,
        latitude: latitude,
        longitude: longitude,
        profileImageUrl: profileImageUrl,
        businessName: businessName,
        description: description,
        serviceCategories: serviceCategories,
        portfolioImages: portfolioImages,
        workingHours: workingHours,
        isAvailable: isAvailable,
        updatedAt: DateTime.now(),
      );

      await _firebaseService.updateUser(updatedUser);
      _currentUser = updatedUser;

      return true;
    } catch (e) {
      _setError('فشل في تحديث الملف الشخصي: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> resetPassword(String email) async {
    try {
      _setLoading(true);
      _clearError();

      await _auth.sendPasswordResetEmail(email: email);
      return true;
    } on FirebaseAuthException catch (e) {
      _setError(_getAuthErrorMessage(e.code));
      return false;
    } catch (e) {
      _setError('حدث خطأ غير متوقع: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  String _getAuthErrorMessage(String errorCode) {
    switch (errorCode) {
      case 'user-not-found':
        return 'لا يوجد مستخدم بهذا البريد الإلكتروني';
      case 'wrong-password':
        return 'كلمة المرور غير صحيحة';
      case 'email-already-in-use':
        return 'البريد الإلكتروني مستخدم بالفعل';
      case 'weak-password':
        return 'كلمة المرور ضعيفة جداً';
      case 'invalid-email':
        return 'البريد الإلكتروني غير صحيح';
      case 'user-disabled':
        return 'تم تعطيل هذا الحساب';
      case 'too-many-requests':
        return 'تم تجاوز عدد المحاولات المسموح، حاول لاحقاً';
      case 'operation-not-allowed':
        return 'هذه العملية غير مسموحة';
      default:
        return 'حدث خطأ في المصادقة: $errorCode';
    }
  }
}
