import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/utils/demo_data.dart';
import '../providers/auth_provider.dart' as auth_provider;

class DemoSetupScreen extends StatefulWidget {
  const DemoSetupScreen({super.key});

  @override
  State<DemoSetupScreen> createState() => _DemoSetupScreenState();
}

class _DemoSetupScreenState extends State<DemoSetupScreen> {
  bool _isCreating = false;
  final List<String> _createdAccounts = [];

  Future<void> _createDemoAccounts() async {
    setState(() {
      _isCreating = true;
      _createdAccounts.clear();
    });

    final authProvider = context.read<auth_provider.AuthProvider>();

    for (final userData in DemoData.demoUsers) {
      try {
        final success = await authProvider.registerWithEmailAndPassword(
          email: userData['email'],
          password: userData['password'],
          name: userData['name'],
          phone: userData['phone'],
          userType: userData['userType'],
          businessName: userData['businessName'],
          description: userData['description'],
          serviceCategories: userData['serviceCategories']?.cast<String>(),
        );

        if (success) {
          _createdAccounts.add('✅ ${userData['email']} - ${userData['name']}');
          // Sign out after creating each account
          await FirebaseAuth.instance.signOut();
        } else {
          _createdAccounts.add('❌ فشل في إنشاء: ${userData['email']}');
        }
      } catch (e) {
        _createdAccounts.add('❌ خطأ في: ${userData['email']} - $e');
      }

      setState(() {});
      await Future.delayed(const Duration(seconds: 1));
    }

    setState(() {
      _isCreating = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعداد الحسابات التجريبية'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'الحسابات التجريبية المتاحة:',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    ...DemoData.demoUsers.map(
                      (user) => Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4),
                        child: Row(
                          children: [
                            Icon(
                              user['userType'] == 'customer'
                                  ? Icons.person
                                  : user['userType'] == 'service_provider'
                                  ? Icons.work
                                  : Icons.admin_panel_settings,
                              color: AppColors.primary,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    '${user['name']} (${user['userType']})',
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  Text(
                                    'البريد: ${user['email']} | كلمة المرور: ${user['password']}',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isCreating ? null : _createDemoAccounts,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: _isCreating
                    ? const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          ),
                          SizedBox(width: 12),
                          Text('جاري إنشاء الحسابات...'),
                        ],
                      )
                    : const Text(
                        'إنشاء جميع الحسابات التجريبية',
                        style: TextStyle(fontSize: 16),
                      ),
              ),
            ),
            const SizedBox(height: 20),
            if (_createdAccounts.isNotEmpty) ...[
              const Text(
                'نتائج الإنشاء:',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Expanded(
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: _createdAccounts
                          .map(
                            (account) => Padding(
                              padding: const EdgeInsets.symmetric(vertical: 2),
                              child: Text(
                                account,
                                style: TextStyle(
                                  fontFamily: 'monospace',
                                  fontSize: 12,
                                  color: account.startsWith('✅')
                                      ? Colors.green[700]
                                      : Colors.red[700],
                                ),
                              ),
                            ),
                          )
                          .toList(),
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
