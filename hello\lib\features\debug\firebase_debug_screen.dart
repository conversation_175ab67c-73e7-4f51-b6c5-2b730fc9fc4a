import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../../core/theme/app_colors.dart';
import '../../firebase_options.dart';

class FirebaseDebugScreen extends StatefulWidget {
  const FirebaseDebugScreen({super.key});

  @override
  State<FirebaseDebugScreen> createState() => _FirebaseDebugScreenState();
}

class _FirebaseDebugScreenState extends State<FirebaseDebugScreen> {
  Map<String, dynamic> _debugInfo = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _runDiagnostics();
  }

  Future<void> _runDiagnostics() async {
    setState(() => _isLoading = true);

    final info = <String, dynamic>{};

    try {
      // Firebase Core
      info['Firebase Initialized'] = Firebase.apps.isNotEmpty;
      info['Default App'] = Firebase.app().name;

      // Firebase Options
      final options = DefaultFirebaseOptions.currentPlatform;
      info['Project ID'] = options.projectId;
      info['API Key'] = '${options.apiKey.substring(0, 10)}...';
      info['Auth Domain'] = options.authDomain;
      info['Storage Bucket'] = options.storageBucket;

      // Firebase Auth
      try {
        final auth = FirebaseAuth.instance;
        info['Auth Instance'] = 'OK';
        info['Current User'] = auth.currentUser?.uid ?? 'None';

        // Test auth connection by checking current user
        info['Auth Connection'] = 'OK';
      } catch (e) {
        info['Auth Connection'] = 'Error: $e';
      }

      // Firestore
      try {
        final firestore = FirebaseFirestore.instance;
        info['Firestore Instance'] = 'OK';

        // Test firestore connection
        await firestore.collection('test').limit(1).get();
        info['Firestore Connection'] = 'OK';
      } catch (e) {
        info['Firestore Connection'] = 'Error: $e';
      }

      // Network connectivity
      try {
        await FirebaseFirestore.instance.enableNetwork();
        info['Network Status'] = 'Online';
      } catch (e) {
        info['Network Status'] = 'Offline or Error: $e';
      }
    } catch (e) {
      info['General Error'] = e.toString();
    }

    setState(() {
      _debugInfo = info;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تشخيص Firebase'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _runDiagnostics,
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Status Card
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                _getOverallStatus()
                                    ? Icons.check_circle
                                    : Icons.error,
                                color: _getOverallStatus()
                                    ? Colors.green
                                    : Colors.red,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'حالة Firebase',
                                style: Theme.of(context).textTheme.titleLarge
                                    ?.copyWith(fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            _getOverallStatus()
                                ? 'Firebase يعمل بشكل صحيح'
                                : 'توجد مشاكل في إعدادات Firebase',
                            style: TextStyle(
                              color: _getOverallStatus()
                                  ? Colors.green
                                  : Colors.red,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Debug Info
                  Text(
                    'معلومات التشخيص',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),

                  ..._debugInfo.entries.map(
                    (entry) => _buildInfoCard(entry.key, entry.value),
                  ),

                  const SizedBox(height: 24),

                  // Actions
                  Text(
                    'الإجراءات المقترحة',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),

                  _buildSuggestionsCard(),
                ],
              ),
            ),
    );
  }

  Widget _buildInfoCard(String key, dynamic value) {
    final isError = value.toString().toLowerCase().contains('error');
    final isOK = value.toString().toLowerCase().contains('ok');

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(
          isError ? Icons.error : (isOK ? Icons.check_circle : Icons.info),
          color: isError ? Colors.red : (isOK ? Colors.green : Colors.blue),
        ),
        title: Text(key),
        subtitle: Text(
          value.toString(),
          style: TextStyle(
            color: isError ? Colors.red : null,
            fontFamily: 'monospace',
          ),
        ),
      ),
    );
  }

  Widget _buildSuggestionsCard() {
    final suggestions = <String>[];

    if (_debugInfo['Auth Connection']?.toString().contains(
          'api-key-not-valid',
        ) ==
        true) {
      suggestions.add('• تحقق من صحة مفتاح API في firebase_options.dart');
      suggestions.add('• تأكد من إنشاء تطبيق ويب في Firebase Console');
    }

    if (_debugInfo['Firestore Connection']?.toString().contains('offline') ==
            true ||
        _debugInfo['Network Status']?.toString().contains('Offline') == true) {
      suggestions.add('• تحقق من اتصال الإنترنت');
      suggestions.add('• تأكد من تفعيل Firestore في Firebase Console');
    }

    if (_debugInfo['Auth Connection']?.toString().contains('Error') == true) {
      suggestions.add('• تأكد من تفعيل Authentication في Firebase Console');
      suggestions.add('• تحقق من إعدادات Email/Password في Sign-in methods');
    }

    if (suggestions.isEmpty) {
      suggestions.add('• جميع الإعدادات تبدو صحيحة');
      suggestions.add('• إذا كانت المشكلة مستمرة، تحقق من Firebase Console');
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.lightbulb, color: Colors.orange),
                const SizedBox(width: 8),
                Text(
                  'اقتراحات الحل',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...suggestions.map(
              (suggestion) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Text(suggestion),
              ),
            ),
          ],
        ),
      ),
    );
  }

  bool _getOverallStatus() {
    final authOK =
        _debugInfo['Auth Connection']?.toString().contains('OK') == true;
    final firestoreOK =
        _debugInfo['Firestore Connection']?.toString().contains('OK') == true;
    final networkOK =
        !(_debugInfo['Network Status']?.toString().contains('Offline') == true);

    return authOK && firestoreOK && networkOK;
  }
}
