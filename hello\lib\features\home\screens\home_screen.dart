import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../../auth/providers/auth_provider.dart';
import '../../location/providers/location_provider.dart';
import '../../services/providers/services_provider.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadInitialData();
    });
  }

  Future<void> _loadInitialData() async {
    final locationProvider = context.read<LocationProvider>();
    final servicesProvider = context.read<ServicesProvider>();

    // Get current location
    await locationProvider.getCurrentLocation();

    // Load nearby service providers
    if (locationProvider.hasLocation) {
      await servicesProvider.loadServiceProviders(
        latitude: locationProvider.currentPosition!.latitude,
        longitude: locationProvider.currentPosition!.longitude,
        radiusKm: 50, // 50km radius
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = context.watch<AuthProvider>();
    final user = authProvider.currentUser;

    if (user == null) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    return Scaffold(
      body: IndexedStack(
        index: _currentIndex,
        children: [_HomeTab(), _ServicesTab(), _RequestsTab(), _ProfileTab()],
      ),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: (index) => setState(() => _currentIndex = index),
        selectedItemColor: AppColors.primary,
        unselectedItemColor: AppColors.textSecondary,
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'الرئيسية'),
          BottomNavigationBarItem(icon: Icon(Icons.build), label: 'الخدمات'),
          BottomNavigationBarItem(
            icon: Icon(Icons.assignment),
            label: 'طلباتي',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: 'الملف الشخصي',
          ),
        ],
      ),
    );
  }
}

class _HomeTab extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final authProvider = context.watch<AuthProvider>();
    final locationProvider = context.watch<LocationProvider>();
    final servicesProvider = context.watch<ServicesProvider>();

    final user = authProvider.currentUser!;
    final isServiceProvider =
        user.userType == AppConstants.userTypeServiceProvider;

    return CustomScrollView(
      slivers: [
        // App Bar
        SliverAppBar(
          expandedHeight: 200,
          floating: false,
          pinned: true,
          backgroundColor: AppColors.primary,
          flexibleSpace: FlexibleSpaceBar(
            title: Text(
              'مرحباً ${user.name}',
              style: const TextStyle(
                color: AppColors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            background: Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [AppColors.primary, AppColors.primaryLight],
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const SizedBox(height: 40),
                  Icon(
                    isServiceProvider ? Icons.work : Icons.person,
                    size: 60,
                    color: AppColors.white,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    isServiceProvider ? 'مقدم خدمة' : 'عميل',
                    style: const TextStyle(
                      color: AppColors.white,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.notifications, color: AppColors.white),
              onPressed: () {
                // TODO: Navigate to notifications
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('الإشعارات قريباً')),
                );
              },
            ),
          ],
        ),

        // Content
        SliverPadding(
          padding: const EdgeInsets.all(16.0),
          sliver: SliverList(
            delegate: SliverChildListDelegate([
              // Location Card
              if (!locationProvider.hasLocation)
                Card(
                  color: AppColors.warning.withOpacity(0.1),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        Icon(
                          Icons.location_off,
                          size: 48,
                          color: AppColors.warning,
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'تحديد الموقع',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        const Text(
                          'يرجى تفعيل الموقع للحصول على أفضل الخدمات القريبة منك',
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 12),
                        ElevatedButton.icon(
                          onPressed: () =>
                              locationProvider.getCurrentLocation(),
                          icon: const Icon(Icons.location_on),
                          label: const Text('تفعيل الموقع'),
                        ),
                      ],
                    ),
                  ),
                ),

              const SizedBox(height: 16),

              // Quick Actions
              Text(
                'الإجراءات السريعة',
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 12),

              if (isServiceProvider) ...[
                // Service Provider Actions
                Row(
                  children: [
                    Expanded(
                      child: _QuickActionCard(
                        icon: Icons.assignment_add,
                        title: 'طلبات جديدة',
                        subtitle: 'عرض الطلبات المتاحة',
                        color: AppColors.info,
                        onTap: () => context.go('/my-requests'),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _QuickActionCard(
                        icon: Icons.edit,
                        title: 'تحديث الملف',
                        subtitle: 'تحديث معلومات الخدمة',
                        color: AppColors.accent,
                        onTap: () => context.go('/edit-profile'),
                      ),
                    ),
                  ],
                ),
              ] else ...[
                // Customer Actions
                Row(
                  children: [
                    Expanded(
                      child: _QuickActionCard(
                        icon: Icons.search,
                        title: 'البحث عن خدمة',
                        subtitle: 'تصفح الخدمات المتاحة',
                        color: AppColors.primary,
                        onTap: () => context.go('/services'),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _QuickActionCard(
                        icon: Icons.add_circle,
                        title: 'طلب خدمة',
                        subtitle: 'إنشاء طلب خدمة جديد',
                        color: AppColors.success,
                        onTap: () => context.go('/create-request'),
                      ),
                    ),
                  ],
                ),
              ],

              const SizedBox(height: 24),

              // Service Categories (for customers)
              if (!isServiceProvider) ...[
                Text(
                  'فئات الخدمات',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 12),
                GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    crossAxisSpacing: 12,
                    mainAxisSpacing: 12,
                    childAspectRatio: 1.2,
                  ),
                  itemCount: AppConstants.serviceCategories.length,
                  itemBuilder: (context, index) {
                    final category = AppConstants.serviceCategories[index];
                    return _ServiceCategoryCard(
                      category: category,
                      onTap: () => context.go('/services/$category'),
                    );
                  },
                ),
              ],

              // Recent Activity (for service providers)
              if (isServiceProvider) ...[
                Text(
                  'النشاط الأخير',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 12),
                Consumer<ServicesProvider>(
                  builder: (context, provider, child) {
                    final recentRequests = provider.serviceRequests
                        .take(3)
                        .toList();

                    if (recentRequests.isEmpty) {
                      return Card(
                        child: Padding(
                          padding: const EdgeInsets.all(24.0),
                          child: Column(
                            children: [
                              Icon(
                                Icons.assignment_outlined,
                                size: 48,
                                color: AppColors.textSecondary,
                              ),
                              const SizedBox(height: 8),
                              const Text(
                                'لا توجد طلبات حالياً',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 4),
                              const Text(
                                'ستظهر هنا الطلبات الجديدة',
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      );
                    }

                    return Column(
                      children: recentRequests.map((request) {
                        return Card(
                          margin: const EdgeInsets.only(bottom: 8),
                          child: ListTile(
                            leading: CircleAvatar(
                              backgroundColor: _getStatusColor(request.status),
                              child: Icon(
                                _getStatusIcon(request.status),
                                color: AppColors.white,
                              ),
                            ),
                            title: Text(request.title),
                            subtitle: Text(_getStatusText(request.status)),
                            trailing: Text(
                              '${request.proposedPrice?.toStringAsFixed(0) ?? '--'} أوقية',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            onTap: () => context.go('/request/${request.id}'),
                          ),
                        );
                      }).toList(),
                    );
                  },
                ),
              ],
            ]),
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case AppConstants.requestStatusPending:
        return AppColors.warning;
      case AppConstants.requestStatusAccepted:
        return AppColors.info;
      case AppConstants.requestStatusInProgress:
        return AppColors.accent;
      case AppConstants.requestStatusCompleted:
        return AppColors.success;
      case AppConstants.requestStatusCancelled:
        return AppColors.error;
      default:
        return AppColors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case AppConstants.requestStatusPending:
        return Icons.pending;
      case AppConstants.requestStatusAccepted:
        return Icons.check_circle;
      case AppConstants.requestStatusInProgress:
        return Icons.work;
      case AppConstants.requestStatusCompleted:
        return Icons.done_all;
      case AppConstants.requestStatusCancelled:
        return Icons.cancel;
      default:
        return Icons.help;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case AppConstants.requestStatusPending:
        return 'في الانتظار';
      case AppConstants.requestStatusAccepted:
        return 'مقبول';
      case AppConstants.requestStatusInProgress:
        return 'قيد التنفيذ';
      case AppConstants.requestStatusCompleted:
        return 'مكتمل';
      case AppConstants.requestStatusCancelled:
        return 'ملغي';
      default:
        return 'غير معروف';
    }
  }
}

class _ServicesTab extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(child: Text('شاشة الخدمات - قيد التطوير')),
    );
  }
}

class _RequestsTab extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(child: Text('شاشة الطلبات - قيد التطوير')),
    );
  }
}

class _ProfileTab extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(child: Text('شاشة الملف الشخصي - قيد التطوير')),
    );
  }
}

class _QuickActionCard extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final Color color;
  final VoidCallback onTap;

  const _QuickActionCard({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.color,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: TextStyle(fontSize: 12, color: AppColors.textSecondary),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _ServiceCategoryCard extends StatelessWidget {
  final String category;
  final VoidCallback onTap;

  const _ServiceCategoryCard({required this.category, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                _getCategoryIcon(category),
                size: 40,
                color: AppColors.primary,
              ),
              const SizedBox(height: 8),
              Text(
                _getCategoryName(category),
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'plumbing':
        return Icons.plumbing;
      case 'carpentry':
        return Icons.carpenter;
      case 'electrical':
        return Icons.electrical_services;
      case 'painting':
        return Icons.format_paint;
      case 'appliance_repair':
        return Icons.build;
      case 'furniture_moving':
        return Icons.local_shipping;
      case 'gardening':
        return Icons.grass;
      default:
        return Icons.build;
    }
  }

  String _getCategoryName(String category) {
    switch (category) {
      case 'plumbing':
        return 'السباكة';
      case 'carpentry':
        return 'النجارة';
      case 'electrical':
        return 'الكهرباء';
      case 'painting':
        return 'الدهان';
      case 'appliance_repair':
        return 'إصلاح الأجهزة';
      case 'furniture_moving':
        return 'نقل الأثاث';
      case 'gardening':
        return 'خدمات الحدائق';
      default:
        return category;
    }
  }
}
