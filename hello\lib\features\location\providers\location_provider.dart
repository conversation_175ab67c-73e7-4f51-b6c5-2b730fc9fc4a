import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get_it/get_it.dart';

import '../../../core/services/location_service.dart';

class LocationProvider extends ChangeNotifier {
  final LocationService _locationService = GetIt.instance<LocationService>();

  Position? _currentPosition;
  String? _currentAddress;
  bool _isLoading = false;
  String? _errorMessage;
  bool _locationPermissionGranted = false;
  bool _locationServiceEnabled = false;

  // Getters
  Position? get currentPosition => _currentPosition;
  String? get currentAddress => _currentAddress;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get locationPermissionGranted => _locationPermissionGranted;
  bool get locationServiceEnabled => _locationServiceEnabled;
  bool get hasLocation => _currentPosition != null;

  LocationProvider() {
    _init();
  }

  Future<void> _init() async {
    await checkLocationPermission();
    await checkLocationService();
  }

  Future<void> checkLocationService() async {
    try {
      _locationServiceEnabled = await _locationService
          .isLocationServiceEnabled();
      notifyListeners();
    } catch (e) {
      _setError('فشل في فحص خدمة الموقع: $e');
    }
  }

  Future<void> checkLocationPermission() async {
    try {
      final permission = await _locationService.checkLocationPermission();
      _locationPermissionGranted =
          permission == LocationPermission.always ||
          permission == LocationPermission.whileInUse;
      notifyListeners();
    } catch (e) {
      _setError('فشل في فحص إذن الموقع: $e');
    }
  }

  Future<bool> requestLocationPermission() async {
    try {
      _setLoading(true);
      _clearError();

      final permission = await _locationService.requestLocationPermission();
      _locationPermissionGranted =
          permission == LocationPermission.always ||
          permission == LocationPermission.whileInUse;

      if (!_locationPermissionGranted) {
        _setError('تم رفض إذن الوصول للموقع');
        return false;
      }

      return true;
    } catch (e) {
      _setError('فشل في طلب إذن الموقع: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> getCurrentLocation() async {
    try {
      _setLoading(true);
      _clearError();

      // Check location service
      if (!_locationServiceEnabled) {
        await checkLocationService();
        if (!_locationServiceEnabled) {
          _setError('خدمة الموقع غير مفعلة');
          return false;
        }
      }

      // Check permission
      if (!_locationPermissionGranted) {
        final granted = await requestLocationPermission();
        if (!granted) {
          return false;
        }
      }

      // Get current position
      _currentPosition = await _locationService.getCurrentPosition();

      if (_currentPosition != null) {
        // Get address from coordinates
        await getAddressFromCoordinates(
          _currentPosition!.latitude,
          _currentPosition!.longitude,
        );
      }

      return _currentPosition != null;
    } catch (e) {
      _setError('فشل في الحصول على الموقع الحالي: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<void> getAddressFromCoordinates(
    double latitude,
    double longitude,
  ) async {
    try {
      _currentAddress = await _locationService.getAddressFromCoordinates(
        latitude,
        longitude,
      );
      notifyListeners();
    } catch (e) {
      _setError('فشل في الحصول على العنوان: $e');
    }
  }

  Future<Position?> getCoordinatesFromAddress(String address) async {
    try {
      _setLoading(true);
      _clearError();

      final locations = await _locationService.getCoordinatesFromAddress(
        address,
      );

      if (locations.isNotEmpty) {
        final location = locations.first;
        _currentPosition = Position(
          latitude: location.latitude,
          longitude: location.longitude,
          timestamp: DateTime.now(),
          accuracy: 0,
          altitude: 0,
          heading: 0,
          speed: 0,
          speedAccuracy: 0,
          altitudeAccuracy: 0,
          headingAccuracy: 0,
        );
        _currentAddress = address;
        notifyListeners();
        return _currentPosition;
      }

      return null;
    } catch (e) {
      _setError('فشل في الحصول على إحداثيات العنوان: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  double? calculateDistance(double latitude, double longitude) {
    if (_currentPosition == null) return null;

    return _locationService.calculateDistance(
      _currentPosition!.latitude,
      _currentPosition!.longitude,
      latitude,
      longitude,
    );
  }

  void setManualLocation(double latitude, double longitude, String? address) {
    _currentPosition = Position(
      latitude: latitude,
      longitude: longitude,
      timestamp: DateTime.now(),
      accuracy: 0,
      altitude: 0,
      heading: 0,
      speed: 0,
      speedAccuracy: 0,
      altitudeAccuracy: 0,
      headingAccuracy: 0,
    );
    _currentAddress = address;
    notifyListeners();
  }

  void clearLocation() {
    _currentPosition = null;
    _currentAddress = null;
    _clearError();
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
