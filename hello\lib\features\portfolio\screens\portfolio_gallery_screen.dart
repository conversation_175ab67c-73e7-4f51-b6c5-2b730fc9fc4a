import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/models/user_model.dart';
import '../../auth/providers/auth_provider.dart';
import '../widgets/portfolio_image_card.dart';
import '../widgets/add_portfolio_image_dialog.dart';

class PortfolioGalleryScreen extends StatefulWidget {
  final UserModel? serviceProvider;
  final bool isOwner;

  const PortfolioGalleryScreen({
    super.key,
    this.serviceProvider,
    this.isOwner = false,
  });

  @override
  State<PortfolioGalleryScreen> createState() => _PortfolioGalleryScreenState();
}

class _PortfolioGalleryScreenState extends State<PortfolioGalleryScreen> {
  late UserModel currentUser;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    currentUser =
        widget.serviceProvider ?? context.read<AuthProvider>().currentUser!;
  }

  Future<void> _addPortfolioImage() async {
    if (!widget.isOwner) return;

    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => const AddPortfolioImageDialog(),
    );

    if (result != null) {
      setState(() => _isLoading = true);

      try {
        final authProvider = context.read<AuthProvider>();
        final updatedImages = List<String>.from(
          currentUser.portfolioImages ?? [],
        );
        updatedImages.add(result['imageUrl']);

        final success = await authProvider.updateProfile(
          portfolioImages: updatedImages,
        );

        if (success) {
          setState(() {
            currentUser = authProvider.currentUser!;
          });

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم إضافة الصورة بنجاح'),
                backgroundColor: Colors.green,
              ),
            );
          }
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  authProvider.errorMessage ?? 'فشل في إضافة الصورة',
                ),
                backgroundColor: AppColors.error,
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('حدث خطأ: $e'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      } finally {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _removePortfolioImage(String imageUrl) async {
    if (!widget.isOwner) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الصورة'),
        content: const Text('هل أنت متأكد من حذف هذه الصورة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: AppColors.error),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() => _isLoading = true);

      try {
        final authProvider = context.read<AuthProvider>();
        final updatedImages = List<String>.from(
          currentUser.portfolioImages ?? [],
        );
        updatedImages.remove(imageUrl);

        final success = await authProvider.updateProfile(
          portfolioImages: updatedImages,
        );

        if (success) {
          setState(() {
            currentUser = authProvider.currentUser!;
          });

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم حذف الصورة بنجاح'),
                backgroundColor: Colors.green,
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('حدث خطأ: $e'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      } finally {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final portfolioImages = currentUser.portfolioImages ?? [];

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.isOwner ? 'معرض أعمالي' : 'معرض الأعمال'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: widget.isOwner
            ? [
                IconButton(
                  onPressed: _isLoading ? null : _addPortfolioImage,
                  icon: const Icon(Icons.add_photo_alternate),
                  tooltip: 'إضافة صورة',
                ),
              ]
            : null,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : portfolioImages.isEmpty
          ? _buildEmptyState()
          : _buildGallery(portfolioImages),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.photo_library_outlined, size: 80, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            widget.isOwner
                ? 'لا توجد صور في معرض أعمالك'
                : 'لا توجد صور في معرض الأعمال',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Text(
            widget.isOwner
                ? 'اضغط على + لإضافة صور أعمالك'
                : 'لم يقم مقدم الخدمة بإضافة صور بعد',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
          ),
          if (widget.isOwner) ...[
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _addPortfolioImage,
              icon: const Icon(Icons.add_photo_alternate),
              label: const Text('إضافة صورة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildGallery(List<String> images) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(Icons.work, color: AppColors.primary),
              const SizedBox(width: 8),
              Text(
                'معرض الأعمال',
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${images.length} صورة',
                  style: TextStyle(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Gallery Grid
          Expanded(
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                childAspectRatio: 1,
              ),
              itemCount: images.length,
              itemBuilder: (context, index) {
                return PortfolioImageCard(
                  imageUrl: images[index],
                  onTap: () => _showImageFullScreen(images, index),
                  onDelete: widget.isOwner
                      ? () => _removePortfolioImage(images[index])
                      : null,
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void _showImageFullScreen(List<String> images, int initialIndex) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            PortfolioImageViewer(images: images, initialIndex: initialIndex),
      ),
    );
  }
}

class PortfolioImageViewer extends StatefulWidget {
  final List<String> images;
  final int initialIndex;

  const PortfolioImageViewer({
    super.key,
    required this.images,
    required this.initialIndex,
  });

  @override
  State<PortfolioImageViewer> createState() => _PortfolioImageViewerState();
}

class _PortfolioImageViewerState extends State<PortfolioImageViewer> {
  late PageController _pageController;
  late int _currentIndex;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        title: Text('${_currentIndex + 1} من ${widget.images.length}'),
      ),
      body: PageView.builder(
        controller: _pageController,
        itemCount: widget.images.length,
        onPageChanged: (index) {
          setState(() => _currentIndex = index);
        },
        itemBuilder: (context, index) {
          return InteractiveViewer(
            child: Center(
              child: Image.network(
                widget.images[index],
                fit: BoxFit.contain,
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return const Center(
                    child: CircularProgressIndicator(color: Colors.white),
                  );
                },
                errorBuilder: (context, error, stackTrace) {
                  return const Center(
                    child: Icon(Icons.error, color: Colors.white, size: 50),
                  );
                },
              ),
            ),
          );
        },
      ),
    );
  }
}
