import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../../auth/providers/auth_provider.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          final user = authProvider.currentUser;

          if (user == null) {
            return const Center(child: CircularProgressIndicator());
          }

          final isServiceProvider =
              user.userType == AppConstants.userTypeServiceProvider;

          return CustomScrollView(
            slivers: [
              // App Bar with Profile Header
              SliverAppBar(
                expandedHeight: 200,
                floating: false,
                pinned: true,
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.white,
                flexibleSpace: FlexibleSpaceBar(
                  title: Text(
                    user.name,
                    style: const TextStyle(
                      color: AppColors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  background: Container(
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [AppColors.primary, AppColors.primaryLight],
                      ),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const SizedBox(height: 40),
                        CircleAvatar(
                          radius: 50,
                          backgroundColor: AppColors.white.withOpacity(0.2),
                          backgroundImage: user.profileImageUrl != null
                              ? CachedNetworkImageProvider(
                                  user.profileImageUrl!,
                                )
                              : null,
                          child: user.profileImageUrl == null
                              ? Icon(
                                  isServiceProvider ? Icons.work : Icons.person,
                                  size: 50,
                                  color: AppColors.white,
                                )
                              : null,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          isServiceProvider ? 'مقدم خدمة' : 'عميل',
                          style: const TextStyle(
                            color: AppColors.white,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                actions: [
                  IconButton(
                    icon: const Icon(Icons.edit, color: AppColors.white),
                    onPressed: () => context.go('/edit-profile'),
                  ),
                ],
              ),

              // Profile Content
              SliverPadding(
                padding: const EdgeInsets.all(16.0),
                sliver: SliverList(
                  delegate: SliverChildListDelegate([
                    // Basic Info Card
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'المعلومات الأساسية',
                              style: Theme.of(context).textTheme.titleLarge
                                  ?.copyWith(fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(height: 16),
                            _InfoRow(
                              icon: Icons.email,
                              label: 'البريد الإلكتروني',
                              value: user.email,
                            ),
                            const SizedBox(height: 12),
                            _InfoRow(
                              icon: Icons.phone,
                              label: 'رقم الهاتف',
                              value: user.phone,
                            ),
                            if (user.address != null) ...[
                              const SizedBox(height: 12),
                              _InfoRow(
                                icon: Icons.location_on,
                                label: 'العنوان',
                                value: user.address!,
                              ),
                            ],
                            const SizedBox(height: 12),
                            _InfoRow(
                              icon: Icons.calendar_today,
                              label: 'تاريخ التسجيل',
                              value: _formatDate(user.createdAt),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Service Provider Specific Info
                    if (isServiceProvider) ...[
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'معلومات الخدمة',
                                style: Theme.of(context).textTheme.titleLarge
                                    ?.copyWith(fontWeight: FontWeight.bold),
                              ),
                              const SizedBox(height: 16),
                              if (user.businessName != null) ...[
                                _InfoRow(
                                  icon: Icons.business,
                                  label: 'اسم النشاط التجاري',
                                  value: user.businessName!,
                                ),
                                const SizedBox(height: 12),
                              ],
                              if (user.description != null) ...[
                                _InfoRow(
                                  icon: Icons.description,
                                  label: 'الوصف',
                                  value: user.description!,
                                ),
                                const SizedBox(height: 12),
                              ],
                              _InfoRow(
                                icon: Icons.verified,
                                label: 'حالة التحقق',
                                value: user.isVerified ? 'محقق' : 'غير محقق',
                                valueColor: user.isVerified
                                    ? AppColors.success
                                    : AppColors.warning,
                              ),
                              const SizedBox(height: 12),
                              _InfoRow(
                                icon: Icons.toggle_on,
                                label: 'حالة التوفر',
                                value: user.isAvailable == true
                                    ? 'متاح'
                                    : 'غير متاح',
                                valueColor: user.isAvailable == true
                                    ? AppColors.success
                                    : AppColors.error,
                              ),
                              if (user.rating != null && user.rating! > 0) ...[
                                const SizedBox(height: 12),
                                _InfoRow(
                                  icon: Icons.star,
                                  label: 'التقييم',
                                  value:
                                      '${user.rating!.toStringAsFixed(1)} (${user.reviewCount ?? 0} تقييم)',
                                  valueColor: AppColors.warning,
                                ),
                              ],
                            ],
                          ),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Service Categories
                      if (user.serviceCategories != null &&
                          user.serviceCategories!.isNotEmpty) ...[
                        Card(
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'فئات الخدمات',
                                  style: Theme.of(context).textTheme.titleLarge
                                      ?.copyWith(fontWeight: FontWeight.bold),
                                ),
                                const SizedBox(height: 12),
                                Wrap(
                                  spacing: 8,
                                  runSpacing: 8,
                                  children: user.serviceCategories!.map((
                                    category,
                                  ) {
                                    return Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 12,
                                        vertical: 6,
                                      ),
                                      decoration: BoxDecoration(
                                        color: AppColors.primary.withOpacity(
                                          0.1,
                                        ),
                                        borderRadius: BorderRadius.circular(20),
                                      ),
                                      child: Text(
                                        _getCategoryName(category),
                                        style: TextStyle(
                                          color: AppColors.primary,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    );
                                  }).toList(),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                      ],
                    ],

                    // Action Buttons
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'الإعدادات',
                              style: Theme.of(context).textTheme.titleLarge
                                  ?.copyWith(fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(height: 16),
                            ListTile(
                              leading: const Icon(
                                Icons.edit,
                                color: AppColors.primary,
                              ),
                              title: const Text('تعديل الملف الشخصي'),
                              trailing: const Icon(Icons.arrow_forward_ios),
                              onTap: () => context.go('/edit-profile'),
                            ),
                            const Divider(),
                            ListTile(
                              leading: const Icon(
                                Icons.notifications,
                                color: AppColors.info,
                              ),
                              title: const Text('الإشعارات'),
                              trailing: const Icon(Icons.arrow_forward_ios),
                              onTap: () {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('الإشعارات قريباً'),
                                  ),
                                );
                              },
                            ),
                            const Divider(),
                            ListTile(
                              leading: const Icon(
                                Icons.help,
                                color: AppColors.accent,
                              ),
                              title: const Text('المساعدة والدعم'),
                              trailing: const Icon(Icons.arrow_forward_ios),
                              onTap: () {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('المساعدة قريباً'),
                                  ),
                                );
                              },
                            ),
                            const Divider(),
                            ListTile(
                              leading: const Icon(
                                Icons.logout,
                                color: AppColors.error,
                              ),
                              title: const Text('تسجيل الخروج'),
                              onTap: () =>
                                  _showLogoutDialog(context, authProvider),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 100), // Bottom padding
                  ]),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _getCategoryName(String category) {
    switch (category) {
      case 'plumbing':
        return 'السباكة';
      case 'carpentry':
        return 'النجارة';
      case 'electrical':
        return 'الكهرباء';
      case 'painting':
        return 'الدهان';
      case 'appliance_repair':
        return 'إصلاح الأجهزة';
      case 'furniture_moving':
        return 'نقل الأثاث';
      case 'gardening':
        return 'خدمات الحدائق';
      default:
        return category;
    }
  }

  void _showLogoutDialog(BuildContext context, AuthProvider authProvider) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('تسجيل الخروج'),
          content: const Text('هل أنت متأكد من أنك تريد تسجيل الخروج؟'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await authProvider.signOut();
                if (context.mounted) {
                  context.go('/login');
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.error,
                foregroundColor: AppColors.white,
              ),
              child: const Text('تسجيل الخروج'),
            ),
          ],
        );
      },
    );
  }
}

class _InfoRow extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;
  final Color? valueColor;

  const _InfoRow({
    required this.icon,
    required this.label,
    required this.value,
    this.valueColor,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 20, color: AppColors.primary),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: valueColor,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
