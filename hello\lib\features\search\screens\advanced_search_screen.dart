import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/models/user_model.dart';
import '../../../core/models/service_request_model.dart';
import '../../auth/providers/auth_provider.dart';
import '../../location/providers/location_provider.dart';

class AdvancedSearchScreen extends StatefulWidget {
  const AdvancedSearchScreen({super.key});

  @override
  State<AdvancedSearchScreen> createState() => _AdvancedSearchScreenState();
}

class _AdvancedSearchScreenState extends State<AdvancedSearchScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  // Search controllers
  final _serviceProviderSearchController = TextEditingController();
  final _requestSearchController = TextEditingController();

  // Filters
  String? _selectedCategory;
  double _maxDistance = 50.0;
  double _minRating = 0.0;
  bool _verifiedOnly = false;
  bool _availableOnly = true;
  String _sortBy = 'rating'; // rating, distance, name, created_at

  // Results
  List<UserModel> _serviceProviders = [];
  List<ServiceRequestModel> _requests = [];
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _serviceProviderSearchController.dispose();
    _requestSearchController.dispose();
    super.dispose();
  }

  Future<void> _searchServiceProviders() async {
    setState(() => _isSearching = true);

    try {
      final locationProvider = context.read<LocationProvider>();
      final currentLocation = locationProvider.currentLocation;

      Query query = FirebaseFirestore.instance
          .collection(AppConstants.usersCollection)
          .where('userType', isEqualTo: AppConstants.userTypeServiceProvider);

      // Apply filters
      if (_selectedCategory != null) {
        query = query.where(
          'serviceCategories',
          arrayContains: _selectedCategory,
        );
      }

      if (_verifiedOnly) {
        query = query.where('isVerified', isEqualTo: true);
      }

      if (_availableOnly) {
        query = query.where('isAvailable', isEqualTo: true);
      }

      final snapshot = await query.get();
      List<UserModel> providers = snapshot.docs
          .map((doc) => UserModel.fromFirestore(doc))
          .toList();

      // Filter by search text
      final searchText = _serviceProviderSearchController.text.toLowerCase();
      if (searchText.isNotEmpty) {
        providers = providers.where((provider) {
          return provider.name.toLowerCase().contains(searchText) ||
              (provider.businessName?.toLowerCase().contains(searchText) ??
                  false) ||
              (provider.description?.toLowerCase().contains(searchText) ??
                  false);
        }).toList();
      }

      // Filter by distance if location is available
      if (currentLocation != null) {
        providers = providers.where((provider) {
          if (provider.latitude == null || provider.longitude == null) {
            return false;
          }

          final distance = locationProvider.calculateDistance(
            currentLocation.latitude,
            currentLocation.longitude,
            provider.latitude!,
            provider.longitude!,
          );

          return distance <= _maxDistance;
        }).toList();
      }

      // Filter by rating
      if (_minRating > 0) {
        providers = providers.where((provider) {
          return (provider.averageRating ?? 0.0) >= _minRating;
        }).toList();
      }

      // Sort results
      _sortProviders(providers);

      setState(() {
        _serviceProviders = providers;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في البحث: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      setState(() => _isSearching = false);
    }
  }

  Future<void> _searchRequests() async {
    setState(() => _isSearching = true);

    try {
      Query query = FirebaseFirestore.instance
          .collection(AppConstants.requestsCollection)
          .where('status', isEqualTo: 'pending');

      // Apply category filter
      if (_selectedCategory != null) {
        query = query.where('category', isEqualTo: _selectedCategory);
      }

      final snapshot = await query.get();
      List<ServiceRequestModel> requests = snapshot.docs
          .map((doc) => ServiceRequestModel.fromFirestore(doc))
          .toList();

      // Filter by search text
      final searchText = _requestSearchController.text.toLowerCase();
      if (searchText.isNotEmpty) {
        requests = requests.where((request) {
          return request.title.toLowerCase().contains(searchText) ||
              request.description.toLowerCase().contains(searchText);
        }).toList();
      }

      // Sort by creation date (newest first)
      requests.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      setState(() {
        _requests = requests;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في البحث: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      setState(() => _isSearching = false);
    }
  }

  void _sortProviders(List<UserModel> providers) {
    final locationProvider = context.read<LocationProvider>();
    final currentLocation = locationProvider.currentLocation;

    switch (_sortBy) {
      case 'rating':
        providers.sort(
          (a, b) => (b.averageRating ?? 0.0).compareTo(a.averageRating ?? 0.0),
        );
        break;
      case 'distance':
        if (currentLocation != null) {
          providers.sort((a, b) {
            if (a.latitude == null || a.longitude == null) return 1;
            if (b.latitude == null || b.longitude == null) return -1;

            final distanceA = locationProvider.calculateDistance(
              currentLocation.latitude,
              currentLocation.longitude,
              a.latitude!,
              a.longitude!,
            );

            final distanceB = locationProvider.calculateDistance(
              currentLocation.latitude,
              currentLocation.longitude,
              b.latitude!,
              b.longitude!,
            );

            return distanceA.compareTo(distanceB);
          });
        }
        break;
      case 'name':
        providers.sort((a, b) => a.name.compareTo(b.name));
        break;
      case 'created_at':
        providers.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('البحث المتقدم'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: AppColors.white,
          labelColor: AppColors.white,
          unselectedLabelColor: AppColors.white.withOpacity(0.7),
          tabs: const [
            Tab(text: 'مقدمو الخدمات'),
            Tab(text: 'الطلبات'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [_buildServiceProvidersTab(), _buildRequestsTab()],
      ),
    );
  }

  Widget _buildServiceProvidersTab() {
    return Column(
      children: [
        // Search and Filters
        Container(
          padding: const EdgeInsets.all(16),
          color: Colors.grey[50],
          child: Column(
            children: [
              // Search Field
              TextField(
                controller: _serviceProviderSearchController,
                decoration: InputDecoration(
                  hintText: 'ابحث عن مقدم خدمة...',
                  prefixIcon: const Icon(Icons.search),
                  suffixIcon: IconButton(
                    onPressed: _searchServiceProviders,
                    icon: const Icon(Icons.filter_list),
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                onSubmitted: (_) => _searchServiceProviders(),
              ),

              const SizedBox(height: 16),

              // Filters Row
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    _buildFilterChip(
                      'الفئة',
                      _selectedCategory != null
                          ? _getCategoryName(_selectedCategory!)
                          : 'الكل',
                      () => _showCategoryDialog(),
                    ),
                    const SizedBox(width: 8),
                    _buildFilterChip(
                      'المسافة',
                      '${_maxDistance.toInt()} كم',
                      () => _showDistanceDialog(),
                    ),
                    const SizedBox(width: 8),
                    _buildFilterChip(
                      'التقييم',
                      _minRating > 0
                          ? '${_minRating.toStringAsFixed(1)}+'
                          : 'الكل',
                      () => _showRatingDialog(),
                    ),
                    const SizedBox(width: 8),
                    _buildFilterChip(
                      'الترتيب',
                      _getSortLabel(_sortBy),
                      () => _showSortDialog(),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Toggle Filters
              Row(
                children: [
                  Expanded(
                    child: CheckboxListTile(
                      title: const Text('موثق فقط'),
                      value: _verifiedOnly,
                      onChanged: (value) {
                        setState(() => _verifiedOnly = value ?? false);
                      },
                      controlAffinity: ListTileControlAffinity.leading,
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  Expanded(
                    child: CheckboxListTile(
                      title: const Text('متاح فقط'),
                      value: _availableOnly,
                      onChanged: (value) {
                        setState(() => _availableOnly = value ?? true);
                      },
                      controlAffinity: ListTileControlAffinity.leading,
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                ],
              ),

              // Search Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _isSearching ? null : _searchServiceProviders,
                  icon: _isSearching
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.search),
                  label: Text(_isSearching ? 'جاري البحث...' : 'بحث'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: AppColors.white,
                  ),
                ),
              ),
            ],
          ),
        ),

        // Results
        Expanded(
          child: _serviceProviders.isEmpty
              ? _buildEmptyState('لا توجد نتائج', 'جرب تعديل معايير البحث')
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _serviceProviders.length,
                  itemBuilder: (context, index) {
                    final provider = _serviceProviders[index];
                    return _ServiceProviderCard(provider: provider);
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildRequestsTab() {
    return Column(
      children: [
        // Search and Filters
        Container(
          padding: const EdgeInsets.all(16),
          color: Colors.grey[50],
          child: Column(
            children: [
              // Search Field
              TextField(
                controller: _requestSearchController,
                decoration: InputDecoration(
                  hintText: 'ابحث في الطلبات...',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                onSubmitted: (_) => _searchRequests(),
              ),

              const SizedBox(height: 16),

              // Category Filter
              DropdownButtonFormField<String>(
                value: _selectedCategory,
                decoration: InputDecoration(
                  labelText: 'فئة الخدمة',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                items: [
                  const DropdownMenuItem(
                    value: null,
                    child: Text('جميع الفئات'),
                  ),
                  ...AppConstants.serviceCategories.map((category) {
                    return DropdownMenuItem(
                      value: category,
                      child: Text(_getCategoryName(category)),
                    );
                  }),
                ],
                onChanged: (value) {
                  setState(() => _selectedCategory = value);
                },
              ),

              const SizedBox(height: 16),

              // Search Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _isSearching ? null : _searchRequests,
                  icon: _isSearching
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.search),
                  label: Text(_isSearching ? 'جاري البحث...' : 'بحث'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: AppColors.white,
                  ),
                ),
              ),
            ],
          ),
        ),

        // Results
        Expanded(
          child: _requests.isEmpty
              ? _buildEmptyState(
                  'لا توجد طلبات',
                  'لا توجد طلبات تطابق معايير البحث',
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _requests.length,
                  itemBuilder: (context, index) {
                    final request = _requests[index];
                    return _RequestCard(request: request);
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildEmptyState(String title, String subtitle) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.search_off, size: 80, color: AppColors.textSecondary),
          const SizedBox(height: 16),
          Text(
            title,
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(color: AppColors.textSecondary),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, String value, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: AppColors.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: AppColors.primary.withOpacity(0.3)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '$label: $value',
              style: TextStyle(
                color: AppColors.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(width: 4),
            Icon(Icons.arrow_drop_down, color: AppColors.primary, size: 18),
          ],
        ),
      ),
    );
  }

  void _showCategoryDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر الفئة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String?>(
              title: const Text('جميع الفئات'),
              value: null,
              groupValue: _selectedCategory,
              onChanged: (value) {
                setState(() => _selectedCategory = value);
                Navigator.pop(context);
              },
            ),
            ...AppConstants.serviceCategories.map((category) {
              return RadioListTile<String>(
                title: Text(_getCategoryName(category)),
                value: category,
                groupValue: _selectedCategory,
                onChanged: (value) {
                  setState(() => _selectedCategory = value);
                  Navigator.pop(context);
                },
              );
            }),
          ],
        ),
      ),
    );
  }

  void _showDistanceDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('المسافة القصوى'),
        content: StatefulBuilder(
          builder: (context, setDialogState) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('${_maxDistance.toInt()} كيلومتر'),
                Slider(
                  value: _maxDistance,
                  min: 1.0,
                  max: 100.0,
                  divisions: 99,
                  onChanged: (value) {
                    setDialogState(() => _maxDistance = value);
                  },
                ),
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              setState(() {});
              Navigator.pop(context);
            },
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }

  void _showRatingDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('الحد الأدنى للتقييم'),
        content: StatefulBuilder(
          builder: (context, setDialogState) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('${_minRating.toStringAsFixed(1)} نجمة'),
                Slider(
                  value: _minRating,
                  min: 0.0,
                  max: 5.0,
                  divisions: 10,
                  onChanged: (value) {
                    setDialogState(() => _minRating = value);
                  },
                ),
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              setState(() {});
              Navigator.pop(context);
            },
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }

  void _showSortDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('ترتيب النتائج'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('التقييم'),
              value: 'rating',
              groupValue: _sortBy,
              onChanged: (value) {
                setState(() => _sortBy = value!);
                Navigator.pop(context);
              },
            ),
            RadioListTile<String>(
              title: const Text('المسافة'),
              value: 'distance',
              groupValue: _sortBy,
              onChanged: (value) {
                setState(() => _sortBy = value!);
                Navigator.pop(context);
              },
            ),
            RadioListTile<String>(
              title: const Text('الاسم'),
              value: 'name',
              groupValue: _sortBy,
              onChanged: (value) {
                setState(() => _sortBy = value!);
                Navigator.pop(context);
              },
            ),
            RadioListTile<String>(
              title: const Text('تاريخ التسجيل'),
              value: 'created_at',
              groupValue: _sortBy,
              onChanged: (value) {
                setState(() => _sortBy = value!);
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  String _getCategoryName(String category) {
    switch (category) {
      case 'plumbing':
        return 'السباكة';
      case 'carpentry':
        return 'النجارة';
      case 'electrical':
        return 'الكهرباء';
      case 'painting':
        return 'الدهان';
      case 'appliance_repair':
        return 'إصلاح الأجهزة';
      case 'furniture_moving':
        return 'نقل الأثاث';
      case 'gardening':
        return 'خدمات الحدائق';
      default:
        return category;
    }
  }

  String _getSortLabel(String sortBy) {
    switch (sortBy) {
      case 'rating':
        return 'التقييم';
      case 'distance':
        return 'المسافة';
      case 'name':
        return 'الاسم';
      case 'created_at':
        return 'الأحدث';
      default:
        return sortBy;
    }
  }
}

class _ServiceProviderCard extends StatelessWidget {
  final UserModel provider;

  const _ServiceProviderCard({required this.provider});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => context.go('/service-provider/${provider.id}'),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    radius: 25,
                    backgroundColor: AppColors.primary,
                    child: Text(
                      provider.name.isNotEmpty
                          ? provider.name[0].toUpperCase()
                          : 'U',
                      style: const TextStyle(
                        color: AppColors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                provider.name,
                                style: Theme.of(context).textTheme.titleMedium
                                    ?.copyWith(fontWeight: FontWeight.bold),
                              ),
                            ),
                            if (provider.isVerified) ...[
                              const Icon(
                                Icons.verified,
                                color: AppColors.success,
                                size: 20,
                              ),
                            ],
                          ],
                        ),
                        if (provider.businessName != null) ...[
                          Text(
                            provider.businessName!,
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(color: AppColors.textSecondary),
                          ),
                        ],
                        Row(
                          children: [
                            Icon(Icons.star, color: Colors.amber, size: 16),
                            const SizedBox(width: 4),
                            Text(
                              '${provider.averageRating?.toStringAsFixed(1) ?? '0.0'} (${provider.reviewCount ?? 0})',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              if (provider.description != null) ...[
                const SizedBox(height: 12),
                Text(
                  provider.description!,
                  style: Theme.of(context).textTheme.bodyMedium,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],

              const SizedBox(height: 12),

              Wrap(
                spacing: 8,
                runSpacing: 4,
                children: (provider.serviceCategories ?? []).map((category) {
                  return Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      _getCategoryName(category),
                      style: TextStyle(
                        color: AppColors.primary,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  );
                }).toList(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getCategoryName(String category) {
    switch (category) {
      case 'plumbing':
        return 'السباكة';
      case 'carpentry':
        return 'النجارة';
      case 'electrical':
        return 'الكهرباء';
      case 'painting':
        return 'الدهان';
      case 'appliance_repair':
        return 'إصلاح الأجهزة';
      case 'furniture_moving':
        return 'نقل الأثاث';
      case 'gardening':
        return 'خدمات الحدائق';
      default:
        return category;
    }
  }
}

class _RequestCard extends StatelessWidget {
  final ServiceRequestModel request;

  const _RequestCard({required this.request});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => context.go('/request/${request.id}'),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                request.title,
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              ),

              const SizedBox(height: 8),

              Text(
                request.description,
                style: Theme.of(context).textTheme.bodyMedium,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 12),

              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      _getCategoryName(request.category),
                      style: TextStyle(
                        color: AppColors.primary,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  const Spacer(),
                  if (request.proposedPrice != null) ...[
                    Icon(
                      Icons.attach_money,
                      size: 16,
                      color: AppColors.success,
                    ),
                    Text(
                      '${request.proposedPrice!.toStringAsFixed(0)} أوقية',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.success,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ],
              ),

              const SizedBox(height: 8),

              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 16,
                    color: AppColors.textSecondary,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _formatDate(request.createdAt),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getCategoryName(String category) {
    switch (category) {
      case 'plumbing':
        return 'السباكة';
      case 'carpentry':
        return 'النجارة';
      case 'electrical':
        return 'الكهرباء';
      case 'painting':
        return 'الدهان';
      case 'appliance_repair':
        return 'إصلاح الأجهزة';
      case 'furniture_moving':
        return 'نقل الأثاث';
      case 'gardening':
        return 'خدمات الحدائق';
      default:
        return category;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
