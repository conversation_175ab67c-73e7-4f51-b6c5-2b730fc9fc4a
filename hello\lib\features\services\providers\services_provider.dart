import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';

import '../../../core/models/user_model.dart';
import '../../../core/models/service_request_model.dart';
import '../../../core/models/review_model.dart';
import '../../../core/services/firebase_service.dart';
import '../../../core/constants/app_constants.dart';

class ServicesProvider extends ChangeNotifier {
  final FirebaseService _firebaseService = GetIt.instance<FirebaseService>();

  List<UserModel> _serviceProviders = [];
  List<ServiceRequestModel> _serviceRequests = [];
  List<ReviewModel> _reviews = [];

  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  List<UserModel> get serviceProviders => _serviceProviders;
  List<ServiceRequestModel> get serviceRequests => _serviceRequests;
  List<ReviewModel> get reviews => _reviews;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // Service Providers
  Future<void> loadServiceProviders({
    String? category,
    double? latitude,
    double? longitude,
    double? radiusKm,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      _serviceProviders = await _firebaseService.getServiceProviders(
        category: category,
        latitude: latitude,
        longitude: longitude,
        radiusKm: radiusKm,
      );
    } catch (e) {
      _setError('فشل في تحميل مقدمي الخدمات: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<UserModel?> getServiceProvider(String providerId) async {
    try {
      return await _firebaseService.getUserById(providerId);
    } catch (e) {
      _setError('فشل في تحميل بيانات مقدم الخدمة: $e');
      return null;
    }
  }

  // Service Requests
  Future<bool> createServiceRequest({
    required String customerId,
    required String serviceCategory,
    required String title,
    required String description,
    required DateTime requestedDate,
    String? serviceProviderId,
    double? proposedPrice,
    String? customerAddress,
    double? customerLatitude,
    double? customerLongitude,
    List<String>? imageUrls,
    String? customerNotes,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      final now = DateTime.now();
      final request = ServiceRequestModel(
        id: '', // Will be set by Firestore
        customerId: customerId,
        serviceProviderId: serviceProviderId,
        serviceCategory: serviceCategory,
        title: title,
        description: description,
        status: AppConstants.requestStatusPending,
        proposedPrice: proposedPrice,
        customerAddress: customerAddress,
        customerLatitude: customerLatitude,
        customerLongitude: customerLongitude,
        requestedDate: requestedDate,
        imageUrls: imageUrls,
        customerNotes: customerNotes,
        createdAt: now,
        updatedAt: now,
      );

      await _firebaseService.createServiceRequest(request);
      return true;
    } catch (e) {
      _setError('فشل في إنشاء طلب الخدمة: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<void> loadServiceRequests({
    String? customerId,
    String? serviceProviderId,
    String? status,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      _serviceRequests = await _firebaseService.getServiceRequests(
        customerId: customerId,
        serviceProviderId: serviceProviderId,
        status: status,
      );
    } catch (e) {
      _setError('فشل في تحميل طلبات الخدمة: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> updateServiceRequestStatus({
    required String requestId,
    required String status,
    double? agreedPrice,
    DateTime? scheduledDate,
    DateTime? completedDate,
    String? providerNotes,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      // Find the request to update
      final requestIndex = _serviceRequests.indexWhere(
        (r) => r.id == requestId,
      );
      if (requestIndex == -1) {
        throw Exception('طلب الخدمة غير موجود');
      }

      final updatedRequest = _serviceRequests[requestIndex].copyWith(
        status: status,
        agreedPrice: agreedPrice,
        scheduledDate: scheduledDate,
        completedDate: completedDate,
        providerNotes: providerNotes,
        updatedAt: DateTime.now(),
      );

      await _firebaseService.updateServiceRequest(updatedRequest);

      // Update local list
      _serviceRequests[requestIndex] = updatedRequest;
      notifyListeners();

      return true;
    } catch (e) {
      _setError('فشل في تحديث حالة الطلب: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Reviews
  Future<bool> createReview({
    required String customerId,
    required String serviceProviderId,
    required String requestId,
    required double rating,
    required String comment,
    List<String>? imageUrls,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      final now = DateTime.now();
      final review = ReviewModel(
        id: '', // Will be set by Firestore
        customerId: customerId,
        serviceProviderId: serviceProviderId,
        requestId: requestId,
        rating: rating,
        comment: comment,
        imageUrls: imageUrls,
        createdAt: now,
        updatedAt: now,
      );

      await _firebaseService.createReview(review);
      return true;
    } catch (e) {
      _setError('فشل في إنشاء التقييم: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<void> loadReviews({
    String? serviceProviderId,
    String? customerId,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      _reviews = await _firebaseService.getReviews(
        serviceProviderId: serviceProviderId,
        customerId: customerId,
      );
    } catch (e) {
      _setError('فشل في تحميل التقييمات: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Helper methods
  List<ServiceRequestModel> getRequestsByStatus(String status) {
    return _serviceRequests
        .where((request) => request.status == status)
        .toList();
  }

  List<ServiceRequestModel> getCustomerRequests(String customerId) {
    return _serviceRequests
        .where((request) => request.customerId == customerId)
        .toList();
  }

  List<ServiceRequestModel> getProviderRequests(String providerId) {
    return _serviceRequests
        .where((request) => request.serviceProviderId == providerId)
        .toList();
  }

  double getAverageRating(String serviceProviderId) {
    final providerReviews = _reviews
        .where((review) => review.serviceProviderId == serviceProviderId)
        .toList();
    if (providerReviews.isEmpty) return 0.0;

    final totalRating = providerReviews.fold<double>(
      0.0,
      (sum, review) => sum + review.rating,
    );
    return totalRating / providerReviews.length;
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  void clearData() {
    _serviceProviders.clear();
    _serviceRequests.clear();
    _reviews.clear();
    _clearError();
    notifyListeners();
  }
}
