import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

import '../../../core/theme/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../providers/services_provider.dart';
import '../../auth/providers/auth_provider.dart';
import '../../location/providers/location_provider.dart';

class CreateRequestScreen extends StatefulWidget {
  final String? category;
  final String? providerId;

  const CreateRequestScreen({super.key, this.category, this.providerId});

  @override
  State<CreateRequestScreen> createState() => _CreateRequestScreenState();
}

class _CreateRequestScreenState extends State<CreateRequestScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _notesController = TextEditingController();
  final _priceController = TextEditingController();

  String? _selectedCategory;
  DateTime _selectedDate = DateTime.now().add(const Duration(days: 1));
  List<File> _selectedImages = [];
  bool _useCurrentLocation = true;

  @override
  void initState() {
    super.initState();
    _selectedCategory = widget.category;
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _notesController.dispose();
    _priceController.dispose();
    super.dispose();
  }

  Future<void> _pickImages() async {
    final ImagePicker picker = ImagePicker();
    final List<XFile> images = await picker.pickMultiImage();

    if (images.isNotEmpty) {
      setState(() {
        _selectedImages = images.map((image) => File(image.path)).toList();
      });
    }
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _submitRequest() async {
    if (!_formKey.currentState!.validate()) return;

    if (_selectedCategory == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار فئة الخدمة'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    final authProvider = context.read<AuthProvider>();
    final servicesProvider = context.read<ServicesProvider>();
    final locationProvider = context.read<LocationProvider>();

    final user = authProvider.currentUser;
    if (user == null) return;

    // Get location data
    String? address;
    double? latitude;
    double? longitude;

    if (_useCurrentLocation && locationProvider.hasLocation) {
      address = locationProvider.currentAddress;
      latitude = locationProvider.currentPosition!.latitude;
      longitude = locationProvider.currentPosition!.longitude;
    }

    // TODO: Upload images to storage
    List<String>? imageUrls;
    if (_selectedImages.isNotEmpty) {
      // For now, we'll skip image upload
      // In a real app, you would upload to Firebase Storage
      imageUrls = [];
    }

    final success = await servicesProvider.createServiceRequest(
      customerId: user.id,
      serviceProviderId: widget.providerId,
      serviceCategory: _selectedCategory!,
      title: _titleController.text.trim(),
      description: _descriptionController.text.trim(),
      requestedDate: _selectedDate,
      proposedPrice: _priceController.text.isNotEmpty
          ? double.tryParse(_priceController.text)
          : null,
      customerAddress: address,
      customerLatitude: latitude,
      customerLongitude: longitude,
      imageUrls: imageUrls,
      customerNotes: _notesController.text.trim().isNotEmpty
          ? _notesController.text.trim()
          : null,
    );

    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إرسال طلب الخدمة بنجاح'),
          backgroundColor: AppColors.success,
        ),
      );
      context.go('/my-requests');
    } else if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(servicesProvider.errorMessage ?? 'فشل في إرسال الطلب'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final locationProvider = context.watch<LocationProvider>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('طلب خدمة جديد'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Service Category
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'فئة الخدمة',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      DropdownButtonFormField<String>(
                        value: _selectedCategory,
                        decoration: const InputDecoration(
                          hintText: 'اختر فئة الخدمة',
                          prefixIcon: Icon(Icons.category),
                        ),
                        items: AppConstants.serviceCategories.map((category) {
                          return DropdownMenuItem(
                            value: category,
                            child: Text(_getCategoryName(category)),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedCategory = value;
                          });
                        },
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى اختيار فئة الخدمة';
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Request Details
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'تفاصيل الطلب',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 16),

                      // Title
                      TextFormField(
                        controller: _titleController,
                        decoration: const InputDecoration(
                          labelText: 'عنوان الطلب',
                          prefixIcon: Icon(Icons.title),
                          hintText: 'مثال: إصلاح صنبور المطبخ',
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى إدخال عنوان الطلب';
                          }
                          if (value.length < 5) {
                            return 'العنوان يجب أن يكون 5 أحرف على الأقل';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // Description
                      TextFormField(
                        controller: _descriptionController,
                        maxLines: 4,
                        decoration: const InputDecoration(
                          labelText: 'وصف المشكلة',
                          prefixIcon: Icon(Icons.description),
                          hintText: 'اشرح المشكلة بالتفصيل...',
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى إدخال وصف المشكلة';
                          }
                          if (value.length < 20) {
                            return 'الوصف يجب أن يكون 20 حرف على الأقل';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // Notes
                      TextFormField(
                        controller: _notesController,
                        maxLines: 2,
                        decoration: const InputDecoration(
                          labelText: 'ملاحظات إضافية (اختياري)',
                          prefixIcon: Icon(Icons.note),
                          hintText: 'أي معلومات إضافية...',
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Date and Price
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'التاريخ والسعر',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 16),

                      // Requested Date
                      ListTile(
                        leading: Icon(
                          Icons.calendar_today,
                          color: AppColors.primary,
                        ),
                        title: const Text('التاريخ المطلوب'),
                        subtitle: Text(
                          '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                        ),
                        trailing: const Icon(Icons.arrow_forward_ios),
                        onTap: _selectDate,
                      ),

                      const Divider(),

                      // Proposed Price
                      TextFormField(
                        controller: _priceController,
                        keyboardType: TextInputType.number,
                        decoration: const InputDecoration(
                          labelText: 'السعر المقترح (اختياري)',
                          prefixIcon: Icon(Icons.attach_money),
                          suffixText: 'أوقية',
                          hintText: '0',
                        ),
                        validator: (value) {
                          if (value != null && value.isNotEmpty) {
                            final price = double.tryParse(value);
                            if (price == null || price <= 0) {
                              return 'يرجى إدخال سعر صحيح';
                            }
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Location
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'الموقع',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),

                      CheckboxListTile(
                        title: const Text('استخدام موقعي الحالي'),
                        subtitle: locationProvider.hasLocation
                            ? Text(
                                locationProvider.currentAddress ?? 'موقع محدد',
                              )
                            : const Text('لم يتم تحديد الموقع'),
                        value: _useCurrentLocation,
                        onChanged: (value) {
                          setState(() {
                            _useCurrentLocation = value ?? false;
                          });
                        },
                        controlAffinity: ListTileControlAffinity.leading,
                      ),

                      if (!locationProvider.hasLocation &&
                          _useCurrentLocation) ...[
                        const SizedBox(height: 8),
                        ElevatedButton.icon(
                          onPressed: () =>
                              locationProvider.getCurrentLocation(),
                          icon: const Icon(Icons.location_on),
                          label: const Text('تحديد الموقع'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.info,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Images
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'الصور (اختياري)',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),

                      if (_selectedImages.isEmpty) ...[
                        OutlinedButton.icon(
                          onPressed: _pickImages,
                          icon: const Icon(Icons.add_photo_alternate),
                          label: const Text('إضافة صور'),
                        ),
                      ] else ...[
                        SizedBox(
                          height: 100,
                          child: ListView.builder(
                            scrollDirection: Axis.horizontal,
                            itemCount: _selectedImages.length + 1,
                            itemBuilder: (context, index) {
                              if (index == _selectedImages.length) {
                                return Container(
                                  width: 100,
                                  margin: const EdgeInsets.only(right: 8),
                                  child: OutlinedButton(
                                    onPressed: _pickImages,
                                    child: const Icon(Icons.add),
                                  ),
                                );
                              }

                              return Container(
                                width: 100,
                                height: 100,
                                margin: const EdgeInsets.only(right: 8),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                  image: DecorationImage(
                                    image: FileImage(_selectedImages[index]),
                                    fit: BoxFit.cover,
                                  ),
                                ),
                                child: Stack(
                                  children: [
                                    Positioned(
                                      top: 4,
                                      right: 4,
                                      child: GestureDetector(
                                        onTap: () {
                                          setState(() {
                                            _selectedImages.removeAt(index);
                                          });
                                        },
                                        child: Container(
                                          padding: const EdgeInsets.all(4),
                                          decoration: BoxDecoration(
                                            color: AppColors.error,
                                            borderRadius: BorderRadius.circular(
                                              12,
                                            ),
                                          ),
                                          child: const Icon(
                                            Icons.close,
                                            size: 16,
                                            color: AppColors.white,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Submit Button
              Consumer<ServicesProvider>(
                builder: (context, servicesProvider, child) {
                  return ElevatedButton(
                    onPressed: servicesProvider.isLoading
                        ? null
                        : _submitRequest,
                    child: servicesProvider.isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                AppColors.white,
                              ),
                            ),
                          )
                        : const Text('إرسال الطلب'),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getCategoryName(String category) {
    switch (category) {
      case 'plumbing':
        return 'السباكة';
      case 'carpentry':
        return 'النجارة';
      case 'electrical':
        return 'الكهرباء';
      case 'painting':
        return 'الدهان';
      case 'appliance_repair':
        return 'إصلاح الأجهزة';
      case 'furniture_moving':
        return 'نقل الأثاث';
      case 'gardening':
        return 'خدمات الحدائق';
      default:
        return category;
    }
  }
}
