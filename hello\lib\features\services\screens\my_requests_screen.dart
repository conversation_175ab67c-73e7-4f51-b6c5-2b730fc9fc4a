import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/models/service_request_model.dart';
import '../../auth/providers/auth_provider.dart';
import '../providers/services_provider.dart';

class MyRequestsScreen extends StatefulWidget {
  const MyRequestsScreen({super.key});

  @override
  State<MyRequestsScreen> createState() => _MyRequestsScreenState();
}

class _MyRequestsScreenState extends State<MyRequestsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadRequests();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadRequests() async {
    final servicesProvider = context.read<ServicesProvider>();
    final authProvider = context.read<AuthProvider>();
    final user = authProvider.currentUser;

    if (user != null) {
      if (user.userType == AppConstants.userTypeServiceProvider) {
        await servicesProvider.loadServiceProviderRequests(user.id);
      } else {
        await servicesProvider.loadCustomerRequests(user.id);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('طلباتي'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
        bottom: Consumer<AuthProvider>(
          builder: (context, authProvider, child) {
            final user = authProvider.currentUser;
            final isServiceProvider =
                user?.userType == AppConstants.userTypeServiceProvider;

            return TabBar(
              controller: _tabController,
              indicatorColor: AppColors.white,
              labelColor: AppColors.white,
              unselectedLabelColor: AppColors.white.withOpacity(0.7),
              tabs: [
                Tab(
                  text: isServiceProvider ? 'الطلبات الواردة' : 'طلباتي النشطة',
                ),
                Tab(
                  text: isServiceProvider
                      ? 'طلباتي المكتملة'
                      : 'طلباتي المكتملة',
                ),
              ],
            );
          },
        ),
      ),
      body: Consumer2<ServicesProvider, AuthProvider>(
        builder: (context, servicesProvider, authProvider, child) {
          final user = authProvider.currentUser;
          if (user == null) {
            return const Center(child: CircularProgressIndicator());
          }

          final isServiceProvider =
              user.userType == AppConstants.userTypeServiceProvider;

          if (servicesProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (servicesProvider.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: AppColors.error),
                  const SizedBox(height: 16),
                  Text(
                    'حدث خطأ',
                    style: Theme.of(context).textTheme.headlineMedium,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    servicesProvider.errorMessage!,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _loadRequests,
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          final allRequests = servicesProvider.serviceRequests;
          final activeRequests = allRequests
              .where(
                (request) =>
                    request.status != 'completed' &&
                    request.status != 'cancelled',
              )
              .toList();
          final completedRequests = allRequests
              .where(
                (request) =>
                    request.status == 'completed' ||
                    request.status == 'cancelled',
              )
              .toList();

          return TabBarView(
            controller: _tabController,
            children: [
              _RequestsList(
                requests: activeRequests,
                emptyMessage: isServiceProvider
                    ? 'لا توجد طلبات واردة حالياً'
                    : 'لا توجد طلبات نشطة حالياً',
                emptySubtitle: isServiceProvider
                    ? 'ستظهر هنا الطلبات الجديدة من العملاء'
                    : 'يمكنك إنشاء طلب خدمة جديد',
                showCreateButton: !isServiceProvider,
              ),
              _RequestsList(
                requests: completedRequests,
                emptyMessage: 'لا توجد طلبات مكتملة',
                emptySubtitle: 'ستظهر هنا الطلبات المكتملة والملغاة',
                showCreateButton: false,
              ),
            ],
          );
        },
      ),
      floatingActionButton: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          final user = authProvider.currentUser;
          final isCustomer = user?.userType == AppConstants.userTypeCustomer;

          if (!isCustomer) return const SizedBox.shrink();

          return FloatingActionButton.extended(
            onPressed: () => context.go('/create-request'),
            backgroundColor: AppColors.primary,
            foregroundColor: AppColors.white,
            icon: const Icon(Icons.add),
            label: const Text('طلب جديد'),
          );
        },
      ),
    );
  }
}

class _RequestsList extends StatelessWidget {
  final List<ServiceRequestModel> requests;
  final String emptyMessage;
  final String emptySubtitle;
  final bool showCreateButton;

  const _RequestsList({
    required this.requests,
    required this.emptyMessage,
    required this.emptySubtitle,
    required this.showCreateButton,
  });

  @override
  Widget build(BuildContext context) {
    if (requests.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.assignment_outlined,
              size: 64,
              color: AppColors.textSecondary,
            ),
            const SizedBox(height: 16),
            Text(
              emptyMessage,
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 8),
            Text(
              emptySubtitle,
              textAlign: TextAlign.center,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
            ),
            if (showCreateButton) ...[
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: () => context.go('/create-request'),
                icon: const Icon(Icons.add),
                label: const Text('إنشاء طلب جديد'),
              ),
            ],
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        final servicesProvider = context.read<ServicesProvider>();
        final authProvider = context.read<AuthProvider>();
        final user = authProvider.currentUser;

        if (user != null) {
          if (user.userType == AppConstants.userTypeServiceProvider) {
            await servicesProvider.loadServiceProviderRequests(user.id);
          } else {
            await servicesProvider.loadCustomerRequests(user.id);
          }
        }
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16.0),
        itemCount: requests.length,
        itemBuilder: (context, index) {
          final request = requests[index];
          return _RequestCard(request: request);
        },
      ),
    );
  }
}

class _RequestCard extends StatelessWidget {
  final ServiceRequestModel request;

  const _RequestCard({required this.request});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => context.go('/request/${request.id}'),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      request.title,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getStatusColor(request.status).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      _getStatusText(request.status),
                      style: TextStyle(
                        color: _getStatusColor(request.status),
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 8),

              Text(
                request.description,
                style: Theme.of(context).textTheme.bodyMedium,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 12),

              Row(
                children: [
                  Icon(
                    Icons.category,
                    size: 16,
                    color: AppColors.textSecondary,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _getCategoryName(request.category),
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  const Spacer(),
                  if (request.proposedPrice != null) ...[
                    Icon(
                      Icons.attach_money,
                      size: 16,
                      color: AppColors.success,
                    ),
                    Text(
                      '${request.proposedPrice!.toStringAsFixed(0)} أوقية',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.success,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ],
              ),

              const SizedBox(height: 8),

              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 16,
                    color: AppColors.textSecondary,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _formatDate(request.createdAt),
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  const Spacer(),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: AppColors.textSecondary,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'pending':
        return AppColors.warning;
      case 'accepted':
        return AppColors.info;
      case 'in_progress':
        return AppColors.primary;
      case 'completed':
        return AppColors.success;
      case 'cancelled':
        return AppColors.error;
      default:
        return AppColors.textSecondary;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'pending':
        return 'في الانتظار';
      case 'accepted':
        return 'مقبول';
      case 'in_progress':
        return 'قيد التنفيذ';
      case 'completed':
        return 'مكتمل';
      case 'cancelled':
        return 'ملغي';
      default:
        return status;
    }
  }

  String _getCategoryName(String category) {
    switch (category) {
      case 'plumbing':
        return 'السباكة';
      case 'carpentry':
        return 'النجارة';
      case 'electrical':
        return 'الكهرباء';
      case 'painting':
        return 'الدهان';
      case 'appliance_repair':
        return 'إصلاح الأجهزة';
      case 'furniture_moving':
        return 'نقل الأثاث';
      case 'gardening':
        return 'خدمات الحدائق';
      default:
        return category;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
