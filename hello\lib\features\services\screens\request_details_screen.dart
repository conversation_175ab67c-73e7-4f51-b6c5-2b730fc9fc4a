import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/models/service_request_model.dart';
import '../../../core/models/user_model.dart';
import '../../auth/providers/auth_provider.dart';
import '../providers/services_provider.dart';

class RequestDetailsScreen extends StatefulWidget {
  final String requestId;

  const RequestDetailsScreen({super.key, required this.requestId});

  @override
  State<RequestDetailsScreen> createState() => _RequestDetailsScreenState();
}

class _RequestDetailsScreenState extends State<RequestDetailsScreen> {
  ServiceRequestModel? _request;
  UserModel? _customer;
  UserModel? _serviceProvider;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadRequestDetails();
  }

  Future<void> _loadRequestDetails() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final servicesProvider = context.read<ServicesProvider>();

      // Load request details
      final request = await servicesProvider.getServiceRequestById(
        widget.requestId,
      );
      if (request == null) {
        setState(() {
          _errorMessage = 'لم يتم العثور على الطلب';
          _isLoading = false;
        });
        return;
      }

      // Load customer and service provider details
      final customer = await servicesProvider.getUserById(request.customerId);
      UserModel? serviceProvider;
      if (request.serviceProviderId != null) {
        serviceProvider = await servicesProvider.getUserById(
          request.serviceProviderId!,
        );
      }

      setState(() {
        _request = request;
        _customer = customer;
        _serviceProvider = serviceProvider;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ في تحميل تفاصيل الطلب: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _updateRequestStatus(String newStatus) async {
    if (_request == null) return;

    final servicesProvider = context.read<ServicesProvider>();
    final authProvider = context.read<AuthProvider>();
    final currentUser = authProvider.currentUser;

    if (currentUser == null) return;

    final success = await servicesProvider.updateServiceRequestStatus(
      _request!.id,
      newStatus,
      currentUser.id,
    );

    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم تحديث حالة الطلب بنجاح'),
          backgroundColor: AppColors.success,
        ),
      );
      _loadRequestDetails(); // Reload to get updated data
    } else if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            servicesProvider.errorMessage ?? 'فشل في تحديث حالة الطلب',
          ),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri phoneUri = Uri(scheme: 'tel', path: phoneNumber);
    if (await canLaunchUrl(phoneUri)) {
      await launchUrl(phoneUri);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('لا يمكن إجراء المكالمة'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تفاصيل الطلب'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
        actions: [
          if (_request != null)
            PopupMenuButton<String>(
              onSelected: (value) {
                switch (value) {
                  case 'refresh':
                    _loadRequestDetails();
                    break;
                  case 'cancel':
                    _showCancelDialog();
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'refresh',
                  child: Row(
                    children: [
                      Icon(Icons.refresh),
                      SizedBox(width: 8),
                      Text('تحديث'),
                    ],
                  ),
                ),
                if (_canCancelRequest())
                  const PopupMenuItem(
                    value: 'cancel',
                    child: Row(
                      children: [
                        Icon(Icons.cancel, color: AppColors.error),
                        SizedBox(width: 8),
                        Text('إلغاء الطلب'),
                      ],
                    ),
                  ),
              ],
            ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: AppColors.error),
            const SizedBox(height: 16),
            Text('حدث خطأ', style: Theme.of(context).textTheme.headlineMedium),
            const SizedBox(height: 8),
            Text(_errorMessage!, textAlign: TextAlign.center),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadRequestDetails,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_request == null) {
      return const Center(child: Text('لم يتم العثور على الطلب'));
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Request Header
          _buildRequestHeader(),
          const SizedBox(height: 16),

          // Request Details
          _buildRequestDetails(),
          const SizedBox(height: 16),

          // Customer Information
          if (_customer != null) ...[
            _buildCustomerInfo(),
            const SizedBox(height: 16),
          ],

          // Service Provider Information
          if (_serviceProvider != null) ...[
            _buildServiceProviderInfo(),
            const SizedBox(height: 16),
          ],

          // Action Buttons
          _buildActionButtons(),
          const SizedBox(height: 100), // Bottom padding
        ],
      ),
    );
  }

  Widget _buildRequestHeader() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    _request!.title,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusColor(_request!.status).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    _getStatusText(_request!.status),
                    style: TextStyle(
                      color: _getStatusColor(_request!.status),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(Icons.category, size: 16, color: AppColors.textSecondary),
                const SizedBox(width: 4),
                Text(
                  _getCategoryName(_request!.category),
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const Spacer(),
                Icon(
                  Icons.access_time,
                  size: 16,
                  color: AppColors.textSecondary,
                ),
                const SizedBox(width: 4),
                Text(
                  _formatDate(_request!.createdAt),
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRequestDetails() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تفاصيل الطلب',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Text(
              _request!.description,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            if (_request!.customerNotes != null) ...[
              const SizedBox(height: 12),
              const Divider(),
              Text(
                'ملاحظات العميل:',
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              Text(
                _request!.customerNotes!,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
            const SizedBox(height: 12),
            const Divider(),
            Row(
              children: [
                Icon(Icons.calendar_today, size: 16, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  'التاريخ المطلوب: ${_formatDate(_request!.requestedDate)}',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
            if (_request!.proposedPrice != null) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.attach_money, size: 16, color: AppColors.success),
                  const SizedBox(width: 8),
                  Text(
                    'السعر المقترح: ${_request!.proposedPrice!.toStringAsFixed(0)} أوقية',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.success,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات العميل',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            ListTile(
              leading: CircleAvatar(
                backgroundColor: AppColors.primary.withOpacity(0.1),
                child: Icon(Icons.person, color: AppColors.primary),
              ),
              title: Text(_customer!.name),
              subtitle: Text(_customer!.phone),
              trailing: IconButton(
                icon: const Icon(Icons.phone, color: AppColors.primary),
                onPressed: () => _makePhoneCall(_customer!.phone),
              ),
            ),
            if (_request!.customerAddress != null) ...[
              const Divider(),
              ListTile(
                leading: Icon(Icons.location_on, color: AppColors.primary),
                title: const Text('العنوان'),
                subtitle: Text(_request!.customerAddress!),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildServiceProviderInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'مقدم الخدمة',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            ListTile(
              leading: CircleAvatar(
                backgroundColor: AppColors.primary.withOpacity(0.1),
                child: Icon(Icons.work, color: AppColors.primary),
              ),
              title: Text(_serviceProvider!.name),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(_serviceProvider!.phone),
                  if (_serviceProvider!.businessName != null)
                    Text(_serviceProvider!.businessName!),
                ],
              ),
              trailing: IconButton(
                icon: const Icon(Icons.phone, color: AppColors.primary),
                onPressed: () => _makePhoneCall(_serviceProvider!.phone),
              ),
            ),
            if (_serviceProvider!.rating != null &&
                _serviceProvider!.rating! > 0) ...[
              const Divider(),
              ListTile(
                leading: Icon(Icons.star, color: AppColors.warning),
                title: Text(
                  'التقييم: ${_serviceProvider!.rating!.toStringAsFixed(1)} (${_serviceProvider!.reviewCount ?? 0} تقييم)',
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    final authProvider = context.watch<AuthProvider>();
    final currentUser = authProvider.currentUser;

    if (currentUser == null) return const SizedBox.shrink();

    final isCustomer = currentUser.id == _request!.customerId;
    final isServiceProvider = currentUser.id == _request!.serviceProviderId;
    final status = _request!.status;

    List<Widget> buttons = [];

    if (isServiceProvider) {
      // Service provider actions
      if (status == 'pending') {
        buttons.addAll([
          ElevatedButton.icon(
            onPressed: () => _updateRequestStatus('accepted'),
            icon: const Icon(Icons.check),
            label: const Text('قبول الطلب'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.success,
              foregroundColor: AppColors.white,
            ),
          ),
          const SizedBox(height: 8),
          OutlinedButton.icon(
            onPressed: () => _updateRequestStatus('cancelled'),
            icon: const Icon(Icons.close),
            label: const Text('رفض الطلب'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.error,
              side: const BorderSide(color: AppColors.error),
            ),
          ),
        ]);
      } else if (status == 'accepted') {
        buttons.add(
          ElevatedButton.icon(
            onPressed: () => _updateRequestStatus('in_progress'),
            icon: const Icon(Icons.play_arrow),
            label: const Text('بدء العمل'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.white,
            ),
          ),
        );
      } else if (status == 'in_progress') {
        buttons.add(
          ElevatedButton.icon(
            onPressed: () => _updateRequestStatus('completed'),
            icon: const Icon(Icons.done),
            label: const Text('إكمال العمل'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.success,
              foregroundColor: AppColors.white,
            ),
          ),
        );
      }
    }

    if (isCustomer && status == 'completed') {
      buttons.add(
        ElevatedButton.icon(
          onPressed: () {
            // TODO: Navigate to rating screen
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('ميزة التقييم قريباً')),
            );
          },
          icon: const Icon(Icons.star),
          label: const Text('تقييم الخدمة'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.warning,
            foregroundColor: AppColors.white,
          ),
        ),
      );
    }

    if (buttons.isEmpty) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: buttons,
        ),
      ),
    );
  }

  bool _canCancelRequest() {
    final authProvider = context.read<AuthProvider>();
    final currentUser = authProvider.currentUser;

    if (currentUser == null || _request == null) return false;

    final isCustomer = currentUser.id == _request!.customerId;
    final status = _request!.status;

    return isCustomer && (status == 'pending' || status == 'accepted');
  }

  void _showCancelDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('إلغاء الطلب'),
          content: const Text('هل أنت متأكد من أنك تريد إلغاء هذا الطلب؟'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('لا'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _updateRequestStatus('cancelled');
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.error,
                foregroundColor: AppColors.white,
              ),
              child: const Text('نعم، إلغاء'),
            ),
          ],
        );
      },
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'pending':
        return AppColors.warning;
      case 'accepted':
        return AppColors.info;
      case 'in_progress':
        return AppColors.primary;
      case 'completed':
        return AppColors.success;
      case 'cancelled':
        return AppColors.error;
      default:
        return AppColors.textSecondary;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'pending':
        return 'في الانتظار';
      case 'accepted':
        return 'مقبول';
      case 'in_progress':
        return 'قيد التنفيذ';
      case 'completed':
        return 'مكتمل';
      case 'cancelled':
        return 'ملغي';
      default:
        return status;
    }
  }

  String _getCategoryName(String category) {
    switch (category) {
      case 'plumbing':
        return 'السباكة';
      case 'carpentry':
        return 'النجارة';
      case 'electrical':
        return 'الكهرباء';
      case 'painting':
        return 'الدهان';
      case 'appliance_repair':
        return 'إصلاح الأجهزة';
      case 'furniture_moving':
        return 'نقل الأثاث';
      case 'gardening':
        return 'خدمات الحدائق';
      default:
        return category;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
