import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/constants/app_constants.dart';

class ServiceCategoriesScreen extends StatelessWidget {
  const ServiceCategoriesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('فئات الخدمات'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'اختر فئة الخدمة التي تحتاجها',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  childAspectRatio: 1.0,
                ),
                itemCount: AppConstants.serviceCategories.length,
                itemBuilder: (context, index) {
                  final category = AppConstants.serviceCategories[index];
                  return _ServiceCategoryCard(
                    category: category,
                    onTap: () => context.go('/services/$category'),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _ServiceCategoryCard extends StatelessWidget {
  final String category;
  final VoidCallback onTap;

  const _ServiceCategoryCard({required this.category, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 6,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                _getCategoryColor(category).withOpacity(0.1),
                _getCategoryColor(category).withOpacity(0.05),
              ],
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: _getCategoryColor(category).withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    _getCategoryIcon(category),
                    size: 40,
                    color: _getCategoryColor(category),
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  _getCategoryName(category),
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: _getCategoryColor(category),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 4),
                Text(
                  _getCategoryDescription(category),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'plumbing':
        return Icons.plumbing;
      case 'carpentry':
        return Icons.carpenter;
      case 'electrical':
        return Icons.electrical_services;
      case 'painting':
        return Icons.format_paint;
      case 'appliance_repair':
        return Icons.build_circle;
      case 'furniture_moving':
        return Icons.local_shipping;
      case 'gardening':
        return Icons.grass;
      default:
        return Icons.build;
    }
  }

  String _getCategoryName(String category) {
    switch (category) {
      case 'plumbing':
        return 'السباكة';
      case 'carpentry':
        return 'النجارة';
      case 'electrical':
        return 'الكهرباء';
      case 'painting':
        return 'الدهان';
      case 'appliance_repair':
        return 'إصلاح الأجهزة';
      case 'furniture_moving':
        return 'نقل الأثاث';
      case 'gardening':
        return 'خدمات الحدائق';
      default:
        return category;
    }
  }

  String _getCategoryDescription(String category) {
    switch (category) {
      case 'plumbing':
        return 'إصلاح الأنابيب والصنابير';
      case 'carpentry':
        return 'أعمال الخشب والأثاث';
      case 'electrical':
        return 'إصلاح الكهرباء والأسلاك';
      case 'painting':
        return 'دهان المنازل والمكاتب';
      case 'appliance_repair':
        return 'إصلاح الأجهزة المنزلية';
      case 'furniture_moving':
        return 'نقل وترتيب الأثاث';
      case 'gardening':
        return 'العناية بالحدائق والنباتات';
      default:
        return 'خدمات متنوعة';
    }
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case 'plumbing':
        return Colors.blue;
      case 'carpentry':
        return Colors.brown;
      case 'electrical':
        return Colors.amber;
      case 'painting':
        return Colors.purple;
      case 'appliance_repair':
        return Colors.green;
      case 'furniture_moving':
        return Colors.orange;
      case 'gardening':
        return Colors.teal;
      default:
        return AppColors.primary;
    }
  }
}
