import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/models/user_model.dart';
import '../../../core/models/review_model.dart';
import '../providers/services_provider.dart';
import '../../location/providers/location_provider.dart';

class ServiceProviderProfileScreen extends StatefulWidget {
  final String providerId;

  const ServiceProviderProfileScreen({super.key, required this.providerId});

  @override
  State<ServiceProviderProfileScreen> createState() =>
      _ServiceProviderProfileScreenState();
}

class _ServiceProviderProfileScreenState
    extends State<ServiceProviderProfileScreen> {
  UserModel? provider;
  List<ReviewModel> reviews = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadProviderData();
  }

  Future<void> _loadProviderData() async {
    setState(() => isLoading = true);

    final servicesProvider = context.read<ServicesProvider>();

    try {
      // Load provider details
      provider = await servicesProvider.getServiceProvider(widget.providerId);

      // Load reviews
      await servicesProvider.loadReviews(serviceProviderId: widget.providerId);
      reviews = servicesProvider.reviews;
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في تحميل البيانات: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      setState(() => isLoading = false);
    }
  }

  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri launchUri = Uri(scheme: 'tel', path: phoneNumber);

    if (await canLaunchUrl(launchUri)) {
      await launchUrl(launchUri);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('لا يمكن إجراء المكالمة'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('ملف مقدم الخدمة'),
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.white,
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (provider == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('ملف مقدم الخدمة'),
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.white,
        ),
        body: const Center(child: Text('لم يتم العثور على مقدم الخدمة')),
      );
    }

    final locationProvider = context.watch<LocationProvider>();
    double? distance;

    if (locationProvider.hasLocation &&
        provider!.latitude != null &&
        provider!.longitude != null) {
      distance = locationProvider.calculateDistance(
        provider!.latitude!,
        provider!.longitude!,
      );
    }

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          // App Bar with Profile Image
          SliverAppBar(
            expandedHeight: 300,
            floating: false,
            pinned: true,
            backgroundColor: AppColors.primary,
            foregroundColor: AppColors.white,
            flexibleSpace: FlexibleSpaceBar(
              title: Text(
                provider!.name,
                style: const TextStyle(
                  color: AppColors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              background: Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [AppColors.primary, AppColors.primaryLight],
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const SizedBox(height: 40),
                    CircleAvatar(
                      radius: 60,
                      backgroundColor: AppColors.white,
                      backgroundImage: provider!.profileImageUrl != null
                          ? CachedNetworkImageProvider(
                              provider!.profileImageUrl!,
                            )
                          : null,
                      child: provider!.profileImageUrl == null
                          ? Icon(
                              Icons.person,
                              size: 60,
                              color: AppColors.primary,
                            )
                          : null,
                    ),
                    const SizedBox(height: 16),
                    if (provider!.businessName != null) ...[
                      Text(
                        provider!.businessName!,
                        style: const TextStyle(
                          color: AppColors.white,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 8),
                    ],
                    // Rating and Reviews
                    if (provider!.rating != null && provider!.rating! > 0) ...[
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          RatingBarIndicator(
                            rating: provider!.rating!,
                            itemBuilder: (context, index) =>
                                const Icon(Icons.star, color: Colors.amber),
                            itemCount: 5,
                            itemSize: 20.0,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '${provider!.rating!.toStringAsFixed(1)} (${provider!.reviewCount ?? 0} تقييم)',
                            style: const TextStyle(
                              color: AppColors.white,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),

          // Content
          SliverPadding(
            padding: const EdgeInsets.all(16.0),
            sliver: SliverList(
              delegate: SliverChildListDelegate([
                // Status and Distance
                Row(
                  children: [
                    // Availability Status
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: provider!.isAvailable == true
                            ? AppColors.success.withOpacity(0.1)
                            : AppColors.error.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            provider!.isAvailable == true
                                ? Icons.check_circle
                                : Icons.cancel,
                            size: 16,
                            color: provider!.isAvailable == true
                                ? AppColors.success
                                : AppColors.error,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            provider!.isAvailable == true
                                ? 'متاح الآن'
                                : 'غير متاح',
                            style: TextStyle(
                              color: provider!.isAvailable == true
                                  ? AppColors.success
                                  : AppColors.error,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const Spacer(),

                    // Distance
                    if (distance != null) ...[
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.info.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.location_on,
                              size: 16,
                              color: AppColors.info,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${distance.toStringAsFixed(1)} كم',
                              style: TextStyle(
                                color: AppColors.info,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),

                const SizedBox(height: 24),

                // Description
                if (provider!.description != null) ...[
                  Text(
                    'نبذة عن الخدمة',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Text(
                        provider!.description!,
                        style: Theme.of(context).textTheme.bodyLarge,
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                ],

                // Service Categories
                if (provider!.serviceCategories != null &&
                    provider!.serviceCategories!.isNotEmpty) ...[
                  Text(
                    'الخدمات المتاحة',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: provider!.serviceCategories!.map((category) {
                          return Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 8,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.primary.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              _getCategoryName(category),
                              style: TextStyle(
                                color: AppColors.primary,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                ],

                // Portfolio Images
                if (provider!.portfolioImages != null &&
                    provider!.portfolioImages!.isNotEmpty) ...[
                  Text(
                    'معرض الأعمال',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  SizedBox(
                    height: 120,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: provider!.portfolioImages!.length,
                      itemBuilder: (context, index) {
                        final imageUrl = provider!.portfolioImages![index];
                        return Container(
                          margin: const EdgeInsets.only(right: 8),
                          width: 120,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            image: DecorationImage(
                              image: CachedNetworkImageProvider(imageUrl),
                              fit: BoxFit.cover,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  const SizedBox(height: 24),
                ],

                // Contact Information
                Text(
                  'معلومات الاتصال',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Card(
                  child: Column(
                    children: [
                      ListTile(
                        leading: Icon(Icons.phone, color: AppColors.primary),
                        title: Text(provider!.phone),
                        subtitle: const Text('رقم الهاتف'),
                        trailing: IconButton(
                          icon: Icon(Icons.call, color: AppColors.success),
                          onPressed: () => _makePhoneCall(provider!.phone),
                        ),
                      ),
                      if (provider!.address != null) ...[
                        const Divider(),
                        ListTile(
                          leading: Icon(
                            Icons.location_on,
                            color: AppColors.primary,
                          ),
                          title: Text(provider!.address!),
                          subtitle: const Text('العنوان'),
                        ),
                      ],
                    ],
                  ),
                ),

                const SizedBox(height: 24),

                // Reviews Section
                Text(
                  'التقييمات والآراء',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),

                if (reviews.isEmpty) ...[
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(24.0),
                      child: Column(
                        children: [
                          Icon(
                            Icons.star_border,
                            size: 48,
                            color: AppColors.textSecondary,
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            'لا توجد تقييمات بعد',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          const Text(
                            'كن أول من يقيم هذا المقدم',
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ),
                ] else ...[
                  ...reviews
                      .take(5)
                      .map((review) => _ReviewCard(review: review)),

                  if (reviews.length > 5) ...[
                    const SizedBox(height: 8),
                    TextButton(
                      onPressed: () {
                        // TODO: Show all reviews
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('عرض جميع التقييمات قريباً'),
                          ),
                        );
                      },
                      child: Text('عرض جميع التقييمات (${reviews.length})'),
                    ),
                  ],
                ],

                const SizedBox(height: 100), // Space for FAB
              ]),
            ),
          ),
        ],
      ),

      // Floating Action Buttons
      floatingActionButton: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          FloatingActionButton(
            heroTag: "call",
            onPressed: () => _makePhoneCall(provider!.phone),
            backgroundColor: AppColors.success,
            child: const Icon(Icons.call, color: AppColors.white),
          ),
          const SizedBox(height: 16),
          FloatingActionButton.extended(
            heroTag: "request",
            onPressed: provider!.isAvailable == true
                ? () => context.go('/create-request?providerId=${provider!.id}')
                : null,
            backgroundColor: provider!.isAvailable == true
                ? AppColors.primary
                : AppColors.grey,
            foregroundColor: AppColors.white,
            icon: const Icon(Icons.message),
            label: const Text('طلب خدمة'),
          ),
        ],
      ),
    );
  }

  String _getCategoryName(String category) {
    switch (category) {
      case 'plumbing':
        return 'السباكة';
      case 'carpentry':
        return 'النجارة';
      case 'electrical':
        return 'الكهرباء';
      case 'painting':
        return 'الدهان';
      case 'appliance_repair':
        return 'إصلاح الأجهزة';
      case 'furniture_moving':
        return 'نقل الأثاث';
      case 'gardening':
        return 'خدمات الحدائق';
      default:
        return category;
    }
  }
}

class _ReviewCard extends StatelessWidget {
  final ReviewModel review;

  const _ReviewCard({required this.review});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundColor: AppColors.primary.withOpacity(0.1),
                  child: Icon(Icons.person, color: AppColors.primary),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'عميل',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      Text(
                        _formatDate(review.createdAt),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                RatingBarIndicator(
                  rating: review.rating,
                  itemBuilder: (context, index) =>
                      const Icon(Icons.star, color: Colors.amber),
                  itemCount: 5,
                  itemSize: 16.0,
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(review.comment, style: Theme.of(context).textTheme.bodyMedium),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }
}
