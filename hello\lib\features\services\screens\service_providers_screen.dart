import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/models/user_model.dart';
import '../providers/services_provider.dart';
import '../../location/providers/location_provider.dart';

class ServiceProvidersScreen extends StatefulWidget {
  final String category;

  const ServiceProvidersScreen({
    super.key,
    required this.category,
  });

  @override
  State<ServiceProvidersScreen> createState() => _ServiceProvidersScreenState();
}

class _ServiceProvidersScreenState extends State<ServiceProvidersScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadServiceProviders();
    });
  }

  Future<void> _loadServiceProviders() async {
    final servicesProvider = context.read<ServicesProvider>();
    final locationProvider = context.read<LocationProvider>();

    await servicesProvider.loadServiceProviders(
      category: widget.category,
      latitude: locationProvider.currentPosition?.latitude,
      longitude: locationProvider.currentPosition?.longitude,
      radiusKm: 50, // 50km radius
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_getCategoryName(widget.category)),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () {
              // TODO: Implement filters
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('الفلاتر قريباً')),
              );
            },
          ),
        ],
      ),
      body: Consumer<ServicesProvider>(
        builder: (context, servicesProvider, child) {
          if (servicesProvider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (servicesProvider.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: AppColors.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'حدث خطأ',
                    style: Theme.of(context).textTheme.headlineMedium,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    servicesProvider.errorMessage!,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _loadServiceProviders,
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          final providers = servicesProvider.serviceProviders;

          if (providers.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.search_off,
                    size: 64,
                    color: AppColors.textSecondary,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'لا توجد خدمات متاحة',
                    style: Theme.of(context).textTheme.headlineMedium,
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'لم نجد مقدمي خدمات في هذه الفئة حالياً',
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => context.go('/create-request?category=${widget.category}'),
                    child: const Text('إنشاء طلب خدمة'),
                  ),
                ],
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: _loadServiceProviders,
            child: ListView.builder(
              padding: const EdgeInsets.all(16.0),
              itemCount: providers.length,
              itemBuilder: (context, index) {
                final provider = providers[index];
                return _ServiceProviderCard(
                  provider: provider,
                  onTap: () => context.go('/provider/${provider.id}'),
                );
              },
            ),
          );
        },
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => context.go('/create-request?category=${widget.category}'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
        icon: const Icon(Icons.add),
        label: const Text('طلب خدمة'),
      ),
    );
  }

  String _getCategoryName(String category) {
    switch (category) {
      case 'plumbing':
        return 'السباكة';
      case 'carpentry':
        return 'النجارة';
      case 'electrical':
        return 'الكهرباء';
      case 'painting':
        return 'الدهان';
      case 'appliance_repair':
        return 'إصلاح الأجهزة';
      case 'furniture_moving':
        return 'نقل الأثاث';
      case 'gardening':
        return 'خدمات الحدائق';
      default:
        return category;
    }
  }
}

class _ServiceProviderCard extends StatelessWidget {
  final UserModel provider;
  final VoidCallback onTap;

  const _ServiceProviderCard({
    required this.provider,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final locationProvider = context.watch<LocationProvider>();
    double? distance;
    
    if (locationProvider.hasLocation && 
        provider.latitude != null && 
        provider.longitude != null) {
      distance = locationProvider.calculateDistance(
        provider.latitude!,
        provider.longitude!,
      );
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // Profile Image
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: AppColors.primary.withOpacity(0.1),
                    backgroundImage: provider.profileImageUrl != null
                        ? CachedNetworkImageProvider(provider.profileImageUrl!)
                        : null,
                    child: provider.profileImageUrl == null
                        ? Icon(
                            Icons.person,
                            size: 30,
                            color: AppColors.primary,
                          )
                        : null,
                  ),
                  
                  const SizedBox(width: 16),
                  
                  // Provider Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          provider.name,
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (provider.businessName != null) ...[
                          const SizedBox(height: 2),
                          Text(
                            provider.businessName!,
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            // Rating
                            if (provider.rating != null && provider.rating! > 0) ...[
                              RatingBarIndicator(
                                rating: provider.rating!,
                                itemBuilder: (context, index) => const Icon(
                                  Icons.star,
                                  color: Colors.amber,
                                ),
                                itemCount: 5,
                                itemSize: 16.0,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '(${provider.reviewCount ?? 0})',
                                style: Theme.of(context).textTheme.bodySmall,
                              ),
                            ] else ...[
                              Text(
                                'جديد',
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: AppColors.info,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                            
                            const Spacer(),
                            
                            // Distance
                            if (distance != null) ...[
                              Icon(
                                Icons.location_on,
                                size: 16,
                                color: AppColors.textSecondary,
                              ),
                              const SizedBox(width: 2),
                              Text(
                                '${distance.toStringAsFixed(1)} كم',
                                style: Theme.of(context).textTheme.bodySmall,
                              ),
                            ],
                          ],
                        ),
                      ],
                    ),
                  ),
                  
                  // Availability Status
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: provider.isAvailable == true
                          ? AppColors.success.withOpacity(0.1)
                          : AppColors.error.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      provider.isAvailable == true ? 'متاح' : 'غير متاح',
                      style: TextStyle(
                        color: provider.isAvailable == true
                            ? AppColors.success
                            : AppColors.error,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              
              // Description
              if (provider.description != null) ...[
                const SizedBox(height: 12),
                Text(
                  provider.description!,
                  style: Theme.of(context).textTheme.bodyMedium,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
              
              // Service Categories
              if (provider.serviceCategories != null && 
                  provider.serviceCategories!.isNotEmpty) ...[
                const SizedBox(height: 12),
                Wrap(
                  spacing: 6,
                  runSpacing: 6,
                  children: provider.serviceCategories!.take(3).map((category) {
                    return Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        _getCategoryName(category),
                        style: TextStyle(
                          color: AppColors.primary,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ],
              
              const SizedBox(height: 12),
              
              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: onTap,
                      icon: const Icon(Icons.visibility),
                      label: const Text('عرض الملف'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: provider.isAvailable == true
                          ? () => context.go('/create-request?providerId=${provider.id}')
                          : null,
                      icon: const Icon(Icons.message),
                      label: const Text('طلب خدمة'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getCategoryName(String category) {
    switch (category) {
      case 'plumbing':
        return 'السباكة';
      case 'carpentry':
        return 'النجارة';
      case 'electrical':
        return 'الكهرباء';
      case 'painting':
        return 'الدهان';
      case 'appliance_repair':
        return 'إصلاح الأجهزة';
      case 'furniture_moving':
        return 'نقل الأثاث';
      case 'gardening':
        return 'خدمات الحدائق';
      default:
        return category;
    }
  }
}