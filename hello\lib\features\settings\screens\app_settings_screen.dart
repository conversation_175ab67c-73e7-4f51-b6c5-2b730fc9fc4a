import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_colors.dart';
import '../../auth/providers/auth_provider.dart';

class AppSettingsScreen extends StatefulWidget {
  const AppSettingsScreen({super.key});

  @override
  State<AppSettingsScreen> createState() => _AppSettingsScreenState();
}

class _AppSettingsScreenState extends State<AppSettingsScreen> {
  bool _notificationsEnabled = true;
  bool _locationEnabled = true;
  bool _darkModeEnabled = false;
  String _selectedLanguage = 'ar';
  bool _autoAcceptRequests = false;
  bool _showOnlineStatus = true;
  double _searchRadius = 10.0;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _notificationsEnabled = prefs.getBool('notifications_enabled') ?? true;
      _locationEnabled = prefs.getBool('location_enabled') ?? true;
      _darkModeEnabled = prefs.getBool('dark_mode_enabled') ?? false;
      _selectedLanguage = prefs.getString('selected_language') ?? 'ar';
      _autoAcceptRequests = prefs.getBool('auto_accept_requests') ?? false;
      _showOnlineStatus = prefs.getBool('show_online_status') ?? true;
      _searchRadius = prefs.getDouble('search_radius') ?? 10.0;
    });
  }

  Future<void> _saveSetting(String key, dynamic value) async {
    final prefs = await SharedPreferences.getInstance();
    if (value is bool) {
      await prefs.setBool(key, value);
    } else if (value is String) {
      await prefs.setString(key, value);
    } else if (value is double) {
      await prefs.setDouble(key, value);
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = context.watch<AuthProvider>();
    final currentUser = authProvider.currentUser;

    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات التطبيق'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // User Info Card
            if (currentUser != null) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      CircleAvatar(
                        radius: 30,
                        backgroundColor: AppColors.primary,
                        child: Text(
                          currentUser.name.isNotEmpty
                              ? currentUser.name[0].toUpperCase()
                              : 'U',
                          style: const TextStyle(
                            color: AppColors.white,
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              currentUser.name,
                              style: Theme.of(context).textTheme.titleLarge
                                  ?.copyWith(fontWeight: FontWeight.bold),
                            ),
                            Text(
                              currentUser.email,
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(color: AppColors.textSecondary),
                            ),
                            Text(
                              _getUserTypeLabel(currentUser.userType),
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(
                                    color: AppColors.primary,
                                    fontWeight: FontWeight.w500,
                                  ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),
            ],

            // General Settings
            _buildSectionTitle('الإعدادات العامة'),
            Card(
              child: Column(
                children: [
                  SwitchListTile(
                    title: const Text('الإشعارات'),
                    subtitle: const Text('تلقي إشعارات الطلبات والرسائل'),
                    value: _notificationsEnabled,
                    onChanged: (value) {
                      setState(() => _notificationsEnabled = value);
                      _saveSetting('notifications_enabled', value);
                    },
                    activeColor: AppColors.primary,
                  ),
                  const Divider(height: 1),
                  SwitchListTile(
                    title: const Text('خدمات الموقع'),
                    subtitle: const Text('السماح بالوصول لموقعك الجغرافي'),
                    value: _locationEnabled,
                    onChanged: (value) {
                      setState(() => _locationEnabled = value);
                      _saveSetting('location_enabled', value);
                    },
                    activeColor: AppColors.primary,
                  ),
                  const Divider(height: 1),
                  SwitchListTile(
                    title: const Text('الوضع الليلي'),
                    subtitle: const Text('تفعيل المظهر الداكن'),
                    value: _darkModeEnabled,
                    onChanged: (value) {
                      setState(() => _darkModeEnabled = value);
                      _saveSetting('dark_mode_enabled', value);
                      _showComingSoon('الوضع الليلي');
                    },
                    activeColor: AppColors.primary,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Language Settings
            _buildSectionTitle('اللغة'),
            Card(
              child: ListTile(
                title: const Text('لغة التطبيق'),
                subtitle: Text(_getLanguageLabel(_selectedLanguage)),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () => _showLanguageDialog(),
              ),
            ),

            const SizedBox(height: 24),

            // Service Provider Settings
            if (currentUser?.userType == 'service_provider') ...[
              _buildSectionTitle('إعدادات مقدم الخدمة'),
              Card(
                child: Column(
                  children: [
                    SwitchListTile(
                      title: const Text('قبول الطلبات تلقائياً'),
                      subtitle: const Text('قبول الطلبات الجديدة بشكل تلقائي'),
                      value: _autoAcceptRequests,
                      onChanged: (value) {
                        setState(() => _autoAcceptRequests = value);
                        _saveSetting('auto_accept_requests', value);
                      },
                      activeColor: AppColors.primary,
                    ),
                    const Divider(height: 1),
                    SwitchListTile(
                      title: const Text('إظهار الحالة الإلكترونية'),
                      subtitle: const Text('إظهار ما إذا كنت متصلاً أم لا'),
                      value: _showOnlineStatus,
                      onChanged: (value) {
                        setState(() => _showOnlineStatus = value);
                        _saveSetting('show_online_status', value);
                      },
                      activeColor: AppColors.primary,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),
            ],

            // Search Settings
            _buildSectionTitle('إعدادات البحث'),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'نطاق البحث: ${_searchRadius.toInt()} كم',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Slider(
                      value: _searchRadius,
                      min: 1.0,
                      max: 50.0,
                      divisions: 49,
                      label: '${_searchRadius.toInt()} كم',
                      onChanged: (value) {
                        setState(() => _searchRadius = value);
                      },
                      onChangeEnd: (value) {
                        _saveSetting('search_radius', value);
                      },
                      activeColor: AppColors.primary,
                    ),
                    Text(
                      'المسافة القصوى للبحث عن مقدمي الخدمات',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Account Settings
            _buildSectionTitle('إعدادات الحساب'),
            Card(
              child: Column(
                children: [
                  ListTile(
                    leading: const Icon(Icons.person, color: AppColors.primary),
                    title: const Text('تعديل الملف الشخصي'),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: () => context.go('/edit-profile'),
                  ),
                  const Divider(height: 1),
                  ListTile(
                    leading: const Icon(Icons.lock, color: AppColors.primary),
                    title: const Text('تغيير كلمة المرور'),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: () => _showComingSoon('تغيير كلمة المرور'),
                  ),
                  const Divider(height: 1),
                  ListTile(
                    leading: const Icon(
                      Icons.security,
                      color: AppColors.primary,
                    ),
                    title: const Text('الخصوصية والأمان'),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: () => _showComingSoon('إعدادات الخصوصية'),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // App Info
            _buildSectionTitle('معلومات التطبيق'),
            Card(
              child: Column(
                children: [
                  ListTile(
                    leading: const Icon(Icons.info, color: AppColors.primary),
                    title: const Text('حول التطبيق'),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: () => _showAboutDialog(),
                  ),
                  const Divider(height: 1),
                  ListTile(
                    leading: const Icon(Icons.help, color: AppColors.primary),
                    title: const Text('المساعدة والدعم'),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: () => _showComingSoon('المساعدة والدعم'),
                  ),
                  const Divider(height: 1),
                  ListTile(
                    leading: const Icon(Icons.policy, color: AppColors.primary),
                    title: const Text('سياسة الخصوصية'),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: () => _showComingSoon('سياسة الخصوصية'),
                  ),
                  const Divider(height: 1),
                  ListTile(
                    leading: const Icon(
                      Icons.bug_report,
                      color: AppColors.primary,
                    ),
                    title: const Text('تشخيص Firebase'),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: () => context.go('/firebase-debug'),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Logout Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _showLogoutDialog(),
                icon: const Icon(Icons.logout),
                label: const Text('تسجيل الخروج'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.error,
                  foregroundColor: AppColors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: Theme.of(
          context,
        ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
      ),
    );
  }

  void _showLanguageDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر اللغة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('العربية'),
              value: 'ar',
              groupValue: _selectedLanguage,
              onChanged: (value) {
                setState(() => _selectedLanguage = value!);
                _saveSetting('selected_language', value!);
                Navigator.pop(context);
                _showComingSoon('تغيير اللغة');
              },
            ),
            RadioListTile<String>(
              title: const Text('English'),
              value: 'en',
              groupValue: _selectedLanguage,
              onChanged: (value) {
                setState(() => _selectedLanguage = value!);
                _saveSetting('selected_language', value!);
                Navigator.pop(context);
                _showComingSoon('تغيير اللغة');
              },
            ),
            RadioListTile<String>(
              title: const Text('Français'),
              value: 'fr',
              groupValue: _selectedLanguage,
              onChanged: (value) {
                setState(() => _selectedLanguage = value!);
                _saveSetting('selected_language', value!);
                Navigator.pop(context);
                _showComingSoon('تغيير اللغة');
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showAboutDialog() {
    showAboutDialog(
      context: context,
      applicationName: 'خدماتي',
      applicationVersion: '1.0.0',
      applicationIcon: Container(
        width: 64,
        height: 64,
        decoration: BoxDecoration(
          color: AppColors.primary,
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Icon(
          Icons.home_repair_service,
          color: AppColors.white,
          size: 32,
        ),
      ),
      children: [
        const Text(
          'تطبيق خدماتي هو منصة لربط العملاء بمقدمي الخدمات المنزلية والمهنية. '
          'يمكنك من خلال التطبيق طلب خدمات متنوعة مثل السباكة والكهرباء والنجارة وغيرها.',
        ),
        const SizedBox(height: 16),
        const Text(
          'تم تطوير التطبيق باستخدام Flutter و Firebase.',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              final authProvider = context.read<AuthProvider>();
              await authProvider.signOut();
              if (mounted) {
                context.go('/login');
              }
            },
            style: TextButton.styleFrom(foregroundColor: AppColors.error),
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );
  }

  void _showComingSoon(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$feature - قريباً'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  String _getUserTypeLabel(String userType) {
    switch (userType) {
      case 'customer':
        return 'عميل';
      case 'service_provider':
        return 'مقدم خدمة';
      case 'admin':
        return 'مدير';
      case 'super_admin':
        return 'مدير عام';
      default:
        return 'مستخدم';
    }
  }

  String _getLanguageLabel(String language) {
    switch (language) {
      case 'ar':
        return 'العربية';
      case 'en':
        return 'English';
      case 'fr':
        return 'Français';
      default:
        return 'العربية';
    }
  }
}
