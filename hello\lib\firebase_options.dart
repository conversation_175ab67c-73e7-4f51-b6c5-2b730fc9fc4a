// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDMF3YAoks-EFmOcB1BqIcCnddEvYjMNL0',
    appId: '1:898085468901:web:a1b2c3d4e5f6g7h8i9j0k1l2',
    messagingSenderId: '898085468901',
    projectId: 'helloflutter-4b79d',
    authDomain: 'helloflutter-4b79d.firebaseapp.com',
    storageBucket: 'helloflutter-4b79d.firebasestorage.app',
    measurementId: 'G-XXXXXXXXXX',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDMF3YAoks-EFmOcB1BqIcCnddEvYjMNL0',
    appId: '1:898085468901:android:1b8fb5053e3218c29e720c',
    messagingSenderId: '898085468901',
    projectId: 'helloflutter-4b79d',
    storageBucket: 'helloflutter-4b79d.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDMF3YAoks-EFmOcB1BqIcCnddEvYjMNL0',
    appId: '1:898085468901:ios:1b8fb5053e3218c29e720c',
    messagingSenderId: '898085468901',
    projectId: 'helloflutter-4b79d',
    storageBucket: 'helloflutter-4b79d.firebasestorage.app',
    iosBundleId: 'com.example.hello',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDMF3YAoks-EFmOcB1BqIcCnddEvYjMNL0',
    appId: '1:898085468901:ios:1b8fb5053e3218c29e720c',
    messagingSenderId: '898085468901',
    projectId: 'helloflutter-4b79d',
    storageBucket: 'helloflutter-4b79d.firebasestorage.app',
    iosBundleId: 'com.example.hello',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyDMF3YAoks-EFmOcB1BqIcCnddEvYjMNL0',
    appId: '1:898085468901:web:1b8fb5053e3218c29e720c',
    messagingSenderId: '898085468901',
    projectId: 'helloflutter-4b79d',
    authDomain: 'helloflutter-4b79d.firebaseapp.com',
    storageBucket: 'helloflutter-4b79d.firebasestorage.app',
  );
}
