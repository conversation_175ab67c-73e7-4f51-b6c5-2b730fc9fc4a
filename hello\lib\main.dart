import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:get_it/get_it.dart';
import 'package:firebase_core/firebase_core.dart';

import 'firebase_options.dart';
import 'core/theme/app_theme.dart';
import 'core/routes/app_router.dart';
import 'core/services/service_locator.dart';
import 'features/auth/providers/auth_provider.dart' as auth_provider;
import 'features/services/providers/services_provider.dart';
import 'features/location/providers/location_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // Setup service locator
  await setupServiceLocator();

  runApp(const KhadamatyApp());
}

class KhadamatyApp extends StatelessWidget {
  const KhadamatyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (_) => GetIt.instance<auth_provider.AuthProvider>(),
        ),
        ChangeNotifierProvider(
          create: (_) => GetIt.instance<ServicesProvider>(),
        ),
        ChangeNotifierProvider(
          create: (_) => GetIt.instance<LocationProvider>(),
        ),
      ],
      child: MaterialApp.router(
        title: 'خدماتي - Khadamaty',
        debugShowCheckedModeBanner: false,
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: ThemeMode.system,
        routerConfig: AppRouter.router,

        // Localization
        locale: const Locale('ar', 'MR'), // Arabic - Mauritania
        supportedLocales: const [
          Locale('ar', 'MR'), // Arabic - Mauritania
          Locale('fr', 'MR'), // French - Mauritania
          Locale('en', 'US'), // English - US
        ],
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
      ),
    );
  }
}
