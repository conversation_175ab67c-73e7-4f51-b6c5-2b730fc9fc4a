name: khadamaty
description: "تطبيق خدماتي - ربط العملاء بمقدمي الخدمات العامة في موريتانيا"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # UI & Navigation
  cupertino_icons: ^1.0.8
  go_router: ^14.6.2
  provider: ^6.1.2
  get_it: ^8.0.2
  
  # Firebase
  firebase_core: ^4.0.0
  cloud_firestore: ^6.0.0
  firebase_storage: ^13.0.0
  firebase_messaging: ^16.0.0
  firebase_auth: ^6.0.1
  
  # Internationalization
  flutter_localizations:
    sdk: flutter
  intl: ^0.20.2
  
  # Location Services
  geolocator: ^13.0.2
  geocoding: ^3.0.0
  
  # Image Handling
  image_picker: ^1.1.2
  cached_network_image: ^3.4.1
  
  # Utilities
  shared_preferences: ^2.3.3
  uuid: ^4.5.1
  url_launcher: ^6.3.1
  
  # UI Components
  flutter_rating_bar: ^4.0.1
  shimmer: ^3.0.0
  fl_chart: ^0.69.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  
  # Internationalization
  generate: true
